<?php

namespace Tests\Unit\Actions\Product;

use App\Actions\Product\ConfirmPreorderPurchase;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Models\Card;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmPreorderTest extends TenantTestCase
{
    #[Test]
    public function it_handles_order_items_from_attributes(): void
    {
        Carbon::setTestNow(now());

        $attributes = $this->cartAttributes();

        $customer = User::factory()->create();
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $expected_order_attributes = [
            'customer_id' => $customer->id,
            'pickup_id' => $attributes['delivery_method_id'],
            'status_id' => OrderStatus::preOrder(),
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ];

        $this->assertDatabaseHas(Order::class, $expected_order_attributes);

        $order = Order::where($expected_order_attributes)->first();

        foreach ($attributes['items'] as $item) {
            $product = Product::find($item['product_id']);

            $this->assertDatabaseHas(OrderItem::class, [
                'order_id' => $order->id,
                'product_id' => $product->id,
                'qty' => $item['quantity'],
                'weight' => $item['quantity'] * $product->weight
            ]);
        }

        Carbon::setTestNow();
    }

    private function cartAttributes(): array
    {
        $payment = Payment::factory()->create();
        $pickup = Pickup::factory()->create();
        $date = Date::factory()->create(['schedule_id' => Schedule::factory()]);

        return [
            'delivery_method_id' => $pickup->id,
            'date_id' => $date->id,
            'notes' => 'some new notes',
            'customer' => [
                'first_name' => 'new first',
                'last_name' => 'new last',
                'phone' => '************',
                'email' => '<EMAIL>',
                'save_for_later' => false,
                'opt_in_to_sms' => true
            ],
            'shipping' => [
                'street' => '123 new st',
                'street_2' => 'apt new',
                'city' => 'new',
                'state' => 'NE',
                'zip' => '45678',
                'country' => 'US',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $payment->key,
                'source_id' => null,
                'save_for_later' => false
            ],
            'items' => [
                [
                    'product_id' => Product::factory()->create()->id,
                    'quantity' => 2
                ]
            ]
        ];
    }

    #[Test]
    public function it_handles_customer_attributes_from_attributes(): void
    {
        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_handles_saving_customer_attributes_for_later(): void
    {
        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['save_for_later'] = true;

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'first_name' => 'new first',
            'last_name' => 'new last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_not_subscribing(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => null]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['opt_in_to_sms'] = false;

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_already_subscribed(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => now()]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();
        $attributes['customer']['opt_in_to_sms'] = true;

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
    }

    #[Test]
    public function it_handles_shipping_information_from_the_cart(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);
    }

    #[Test]
    public function it_handles_saving_shipping_information_for_later(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();
        $attributes['shipping']['save_for_later'] = true;

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'street' => '123 new st',
            'street_2' => 'apt new',
            'city' => 'new',
            'state' => 'NE',
            'zip' => '45678',
            'country' => 'US',
        ]);
    }

    #[Test]
    public function it_handles_billing_information_from_the_cart(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'checkout_card_id' => 0,
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();


        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $expected_payment = Payment::where('key', $attributes['billing']['method'])->first();

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => null,
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'checkout_card_id' => 0,
        ]);
    }

    #[Test]
    public function it_handles_saving_billing_information_for_later(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'checkout_card_id' => 0,
        ]);
        $card = Card::factory()->create(['user_id' => $customer->id]);

        $expected_payment = Payment::where(['key' => 'card'])->firstOrNew();
        $expected_payment->fillable(['key']);
        $expected_payment->fill(Payment::factory()->make(['key' => 'card'])->toArray());
        $expected_payment->save();

        $attributes = $this->cartAttributes();
        $attributes['billing']['save_for_later'] = true;
        $attributes['billing']['method'] = 'card';
        $attributes['billing']['source_id'] = $card->source_id;

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => $card->id,
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'checkout_card_id' => $card->id,
        ]);
    }

    #[Test]
    public function it_handles_saving_customer_notes(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create();
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);

        $attributes = $this->cartAttributes();

        (new ConfirmPreorderPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'customer_notes' => 'some new notes',
        ]);
    }
}
