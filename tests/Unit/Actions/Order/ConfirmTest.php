<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Billing\ProcessOrderPayment;
use App\Actions\Order\Confirm;
use App\Models\Card;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\Pickup;
use App\Models\PickupFee;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmTest extends TenantTestCase
{
    #[Test]
    public function it_updates_various_order_attributes_with_defaults(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create(['accounting_id' => 'some accounting id']);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create(['delivery_rate' => 10.00, 'schedule_id' => null]);
        Setting::updateOrCreate(['key' => 'default_parcel_count'], ['value' => '5']);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 2,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 0,
            'delivery_fee_type' => 2,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 1,
            'pickup_date' => null,
            'original_pickup_date' => null,
            'deadline_date' => null,
            'schedule_id' => null,
            'delivery_rate' => 1000,
            'delivery_fee_type' => 1,
            'accounting_id' => 'some accounting id',
            'confirmed' => true,
            'containers' => 5,
            'confirmed_date' => today()->format('Y-m-d'),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
            'first_time_order' => 1
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_various_order_attributes_with_pickup_settings_when_present(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        $customer = User::factory()->create();
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create([
            'schedule_id' => $schedule->id,
            'delivery_rate' => 10.00,
            'settings' => [
                'sales_channel' => 2,
                'delivery_fee_type' => 2
            ]
        ]);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 1,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 0,
            'delivery_fee_type' => 1,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
            'status_id' => OrderStatus::preOrder()
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 2,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 1000,
            'delivery_fee_type' => 2,
            'confirmed' => true,
            'containers' => 1,
            'confirmed_date' => today()->format('Y-m-d'),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_various_order_attributes_with_default_pickup_settings_when_present(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        $customer = User::factory()->create();
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create([
            'schedule_id' => $schedule->id,
            'delivery_rate' => 10.00,
            'settings' => [
                'sales_channel' => 1,
                'delivery_fee_type' => 2
            ]
        ]);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 0,
            'delivery_fee_type' => 1,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
            'status_id' => OrderStatus::preOrder()
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 1,
            'original_pickup_date' => null,
            'schedule_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'delivery_rate' => 1000,
            'delivery_fee_type' => 2,
            'confirmed' => true,
            'containers' => 1,
            'confirmed_date' => today()->format('Y-m-d'),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_sets_date_columns_when_schedule_has_active_order_window(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2),
            'order_end_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(4)
        ]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'pickup_date' => null,
            'original_pickup_date' =>null,
            'deadline_date' => null,
            'schedule_id' => null,
        ]);
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $date->pickup_date->format('Y-m-d'),
            'deadline_date' => $date->order_end_date->format('Y-m-d'),
            'schedule_id' => $schedule->id,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_sets_email_alt_when_customer_has_one(): void
    {
        $customer = User::factory()->create(['email_alt' => '<EMAIL>']);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'customer_email_alt' => null,
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'customer_id' => $customer->id,
            'customer_email_alt' => '<EMAIL>'
        ]);
    }

    #[Test]
    public function it_updates_various_order_attributes_with_custom_params(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create();
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create();

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 1,
            'payment_source_id' => 2,
            'payment_id' => 2,
            'customer_first_name' => 'First',
            'customer_last_name' => 'Last',
            'customer_phone' => '7890-456-123',
            'customer_email' => '<EMAIL>',
            'shipping_street' => '456 Main St',
            'shipping_street_2' => 'Apt 2',
            'shipping_city' => 'City',
            'shipping_state' => 'OH',
            'shipping_zip' => '45678',
            'customer_notes' => '',
        ]);

        $custom_parameters = [
            'type_id' => 3,
            'payment_source_id' => 1,
            'payment_id' => 1,
            'customer_first_name' => 'Custom First',
            'customer_last_name' => 'Custom Last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'shipping_street' => '123 Custom St',
            'shipping_street_2' => 'Apt 1',
            'shipping_city' => 'Test',
            'shipping_state' => 'IL',
            'shipping_zip' => '12345',
            'customer_notes' => 'some custom notes',
        ];

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order, $custom_parameters);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 3,
            'payment_source_id' => 1,
            'payment_id' => 1,
            'customer_first_name' => 'Custom First',
            'customer_last_name' => 'Custom Last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'shipping_street' => '123 Custom St',
            'shipping_street_2' => 'Apt 1',
            'shipping_city' => 'Test',
            'shipping_state' => 'IL',
            'shipping_zip' => '12345',
            'containers' => 1,
            'customer_notes' => 'some custom notes',
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_order_with_custom_date_when_found(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2),
            'order_end_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(4)
        ]);
        $expected_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->addDays(2),
            'order_end_date' => today()->addDays(4),
            'pickup_date' => today()->addDays(6)
        ]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'pickup_date' => null,
            'original_pickup_date' =>null,
            'deadline_date' => null,
            'schedule_id' => Schedule::factory(),
        ]);
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order, [
            'pickup_date' => $expected_date->id
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'deadline_date' => $expected_date->order_end_date->format('Y-m-d'),
            'schedule_id' => $schedule->id,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_update_order_with_custom_date_when_not_found(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();
        $expected_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2),
            'order_end_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(4)
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'pickup_date' => null,
            'original_pickup_date' =>null,
            'deadline_date' => null,
            'schedule_id' => Schedule::factory(),
        ]);
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order confirmation'
                )
                ->andReturn(OrderPayment::factory()->create(['order_id' => $order->id]));
        });

        (new Confirm)->handle($order, [
            'pickup_date' => 'abc'
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'deadline_date' => $expected_date->order_end_date->format('Y-m-d'),
            'schedule_id' => $schedule->id,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_syncs_pickup_fees_when_they_are_present(): void
    {
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();
        $fees = PickupFee::factory()->times(2)->create([
            'pickup_id' => $pickup->id
        ]);

        $order = Order::factory()->create(['pickup_id' => $pickup->id]);
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);
        $existing_fee = OrderFee::factory()->create(['order_id' => $order->id]);

        (new Confirm)->handle($order);

        $this->assertDatabaseMissing(OrderFee::class, ['id' => $existing_fee->id]);

        foreach ($fees as $fee) {
            /** @var PickupFee $fee */
            $this->assertDatabaseHas(OrderFee::class, [
                'order_id' => $order->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        }
    }

    #[Test]
    public function it_does_not_sync_pickup_fees_when_customer_is_exempt_from_fees(): void
    {
        /** @var Pickup $pickup */
        $customer = User::factory()->create(['exempt_from_fees' => true]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create();
        $fees = PickupFee::factory()->times(2)->create([
            'pickup_id' => $pickup->id
        ]);

        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'customer_id' => $customer->id]);
        $existing_fee = OrderFee::factory()->create(['order_id' => $order->id]);

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(OrderFee::class, ['id' => $existing_fee->id]);

        foreach ($fees as $fee) {
            /** @var PickupFee $fee */
            $this->assertDatabaseMissing(OrderFee::class, [
                'order_id' => $order->id,
                'title' => $fee->title,
            ]);
        }
    }

    #[Test]
    public function it_deducts_product_inventory(): void
    {
        $order = Order::factory()->create();
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);

        $product_one = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 8]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product_one->id, 'qty' => 2]);

        $product_two = Product::factory()->create(['track_inventory' => 'no', 'inventory' => 8]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product_two->id, 'qty' => 2]);

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_one->id,
            'inventory' => 6
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product_two->id,
            'inventory' => 8
        ]);
    }

    #[Test]
    public function it_syncs_customer_attributes(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create([
            'last_purchase' => now()->subDays(5),
            'order_count' => 3,
            'recurring_order_count' => 2
        ]);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $order = Order::factory()->create(['is_recurring' => true, 'customer_id' => $customer->id]);

        (new Confirm)->handle($order);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'last_purchase' => now(),
            'order_count' => 4,
            'recurring_order_count' => 3
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_returns_the_updated_order(): void
    {
        $order = Order::factory()->create(['total' => 0, 'subtotal' => 0]);
        Card::factory()->create([
            'user_id' => $order->customer_id,
            'source_id' => 'card_123'
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'unit_price' => 300,
            'qty' => 2
        ]);

        $updated_order = (new Confirm)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'total' => 600,
            'subtotal' => 600
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'subtotal' => 600
        ]);

        $this->assertEquals(600, $updated_order->total);
        $this->assertEquals(600, $updated_order->subtotal);
    }

    #[Test]
    public function it_updates_first_time_order_attribute(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create(['accounting_id' => 'some accounting id']);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create(['delivery_rate' => 10.00, 'schedule_id' => null]);
        Setting::updateOrCreate(['key' => 'default_parcel_count'], ['value' => '5']);

        $expectedOrder = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 2,
            'pickup_date' => today(),
            'original_pickup_date' =>today(),
            'deadline_date' => today(),
            'schedule_id' => Schedule::factory(),
            'delivery_rate' => 0,
            'delivery_fee_type' => 2,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
        ]);

        $unexpectedOrder = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 2,
            'pickup_date' => today(),
            'original_pickup_date' =>today(),
            'deadline_date' => today(),
            'schedule_id' => Schedule::factory(),
            'delivery_rate' => 0,
            'delivery_fee_type' => 2,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
        ]);

        (new Confirm)->handle($expectedOrder);
        (new Confirm)->handle($unexpectedOrder);

        $this->assertDatabaseHas(Order::class, [
            'id' => $expectedOrder->id,
            'first_time_order' => 1
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $unexpectedOrder->id,
            'first_time_order' => 0
        ]);

        Carbon::setTestNow();
    }
}
