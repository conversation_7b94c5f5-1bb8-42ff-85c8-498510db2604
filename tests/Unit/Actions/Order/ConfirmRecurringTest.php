<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Billing\ProcessOrderPayment;
use App\Actions\Order\ConfirmRecurring;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Support\Carbon;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmRecurringTest extends TenantTestCase
{
    #[Test]
    public function it_applies_order_minimum_tag_when_order_is_below_the_pickup_minimum(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => 500]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::MINIMUM_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_applies_exclusivity_tag_when_order_is_below_the_pickup_minimum(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => 500]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create();

        $pickup->products()->save($product);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::EXCLUSIVITY_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_applies_address_error_tag_when_order_is_outside_region(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => 500, 'fulfillment_type' => 2]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::ADDRESS_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_applies_inventory_error_tag_when_order_is_over_product_order_limit(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 100, 'settings' => ['quantity_limit' => 1]]);

        $pickup->products()->save($product);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::INVENTORY_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_applies_inventory_error_tag_when_order_is_over_product_stock_is_short(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 1]);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::INVENTORY_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'stock_status' => 'short',
            'original_qty' => 2,
            'qty' => 2,
            'fulfilled_qty' => 1
        ]);
    }

    #[Test]
    public function it_applies_inventory_error_tag_when_order_is_over_product_stock_is_out(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 0]);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::INVENTORY_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'stock_status' => 'out',
            'original_qty' => 2,
            'qty' => 2,
            'fulfilled_qty' => 0
        ]);
    }

    #[Test]
    public function it_can_apply_multiple_tags(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => 500]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $minimum_tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::MINIMUM_ERROR
        ]);

        $inventory_tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::INVENTORY_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $product = Product::factory()->create(['inventory' => 0]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $minimum_tag->id
        ]);

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $inventory_tag->id
        ]);
    }

    #[Test]
    public function it_does_not_apply_duplicate_tags(): void
    {
        $pickup = Pickup::factory()->create(['min_customer_orders' => 500]);
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $tag = Tag::factory()->create([
            'type' => Tag::type('order'),
            'title' => Tag::MINIMUM_ERROR
        ]);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $order->tags()->save($tag);

        $this->assertEquals(1, $order->tags()->count());

        (new ConfirmRecurring)->handle($order);

        $this->assertEquals(1, $order->tags()->count());

        $this->assertDatabaseHas('order_tag', [
            'order_id' => $order->id,
            'tag_id' => $tag->id
        ]);
    }

    #[Test]
    public function it_does_not_deduct_product_when_order_is_over_product_stock_is_out(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 0]);

        $pickup->products()->save($product);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);
    }

    #[Test]
    public function it_deducts_product_inventory_when_order_is_over_product_stock_is_short(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 1]);

        $pickup->products()->save($product);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 0
        ]);
    }

    #[Test]
    public function it_deducts_product_inventory_when_order_is_over_product_stock_is_full(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 100]);

        $pickup->products()->save($product);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 98
        ]);
    }

    #[Test]
    public function it_updates_item_totals_when_product_stock_is_out(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 0, 'weight' => 1.5]);

        $pickup->products()->save($product);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'stock_status' => 'full',
            'unit_price' => 1234,
            'qty' => 2,
            'fulfilled_qty' => 2,
            'subtotal' => 2468,
            'weight' => 3.0
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'stock_status' => 'out',
            'qty' => 2,
            'fulfilled_qty' => 0,
            'subtotal' => 0,
            'weight' => 0,
        ]);
    }

    #[Test]
    public function it_updates_item_totals_when_product_stock_is_short(): void
    {
        $pickup = Pickup::factory()->create();
        $blueprint = RecurringOrder::factory()->create(['fulfillment_id' => $pickup->id]);

        $product = Product::factory()->create(['inventory' => 1, 'weight' => 1.5]);

        $pickup->products()->save($product);

        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'pickup_id' => $pickup->id
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'stock_status' => 'full',
            'unit_price' => 1234,
            'qty' => 2,
            'fulfilled_qty' => 2,
            'subtotal' => 2468,
            'weight' => 3.0
        ]);

        (new ConfirmRecurring)->handle($order);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'stock_status' => 'short',
            'qty' => 2,
            'fulfilled_qty' => 1,
            'subtotal' => 1234,
            'weight' => 1.500,
        ]);
    }

    #[Test]
    public function it_updates_various_order_attributes_with_defaults(): void
    {
        Carbon::setTestNow(now());

        $customer = User::factory()->create(['accounting_id' => 'some accounting id']);
        Card::factory()->create([
            'user_id' => $customer->id,
            'source_id' => 'card_123'
        ]);
        $pickup = Pickup::factory()->create(['delivery_rate' => 10.00, 'schedule_id' => null]);
        Setting::updateOrCreate(['key' => 'default_parcel_count'], ['value' => '5']);

        $order = Order::factory()->create([
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 2,
            'pickup_date' => today(),
            'original_pickup_date' =>today(),
            'deadline_date' => today(),
            'schedule_id' => Schedule::factory(),
            'delivery_rate' => 0,
            'delivery_fee_type' => 2,
            'accounting_id' => '',
            'confirmed' => false,
            'confirmed_date' => null,
            'created_year' => today()->addYear()->year,
            'created_month' => today()->addMonth()->month,
            'created_day' => today()->addDay()->day,
        ]);

        (new ConfirmRecurring())->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $pickup->id,
            'customer_id' => $customer->id,
            'type_id' => 1,
            'pickup_date' => today()->format('Y-m-d'),
            'original_pickup_date' => today()->format('Y-m-d'),
            'deadline_date' => today()->format('Y-m-d'),
            'schedule_id' => $order->schedule_id,
            'delivery_rate' => 1000,
            'delivery_fee_type' => 1,
            'accounting_id' => 'some accounting id',
            'confirmed' => 1,
            'containers' => 5,
            'confirmed_date' => today()->format('Y-m-d'),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day
        ]);

        Carbon::setTestNow();
    }
}