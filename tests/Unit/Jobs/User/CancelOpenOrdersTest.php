<?php

namespace Tests\Unit\Jobs\User;

use App\Events\User\UserWasDeleted;
use App\Jobs\User\CancelOpenOrders;
use App\Listeners\User\UserWasDeleted\HandleUserDeletion;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CancelOpenOrdersTest extends TenantTestCase
{
    #[Test]
    public function it_job_dispatches_event_when_executed(): void
    {
        Queue::fake();

        $deleted_user = User::factory()->create();

        Order::factory()->create(['customer_id' => $deleted_user->id, 'status_id' => OrderStatus::confirmed()]);

        (new HandleUserDeletion())->handle(new UserWasDeleted($deleted_user->id, $deleted_user->email));

        Queue::assertPushed(CancelOpenOrders::class, fn($job) => $job->userId === $deleted_user->id);
    }

    #[Test]
    public function it_cancels_all_open_orders_of_a_user(): void
    {
        $pickup = Pickup::factory()->create();
        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        $openOrders = Order::factory(3)->create([
            'customer_id' => $user->id,
            'pickup_id' => $pickup->id,
            'status_id' => OrderStatus::confirmed(),
            'canceled' => false,
            'canceled_at' => null,
            'confirmed' => true,
            'deadline_date' => now()->addDays(2),
            'pickup_date' => now()->addDays(3),
            'blueprint_id' => RecurringOrder::factory()
        ]);

        Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::canceled(),
            'canceled' => true,
            'confirmed' => true,
        ]);

        $this->assertEquals(1, Order::where('status_id', OrderStatus::canceled())->count());

        (new CancelOpenOrders($user->id))->handle();

        $openOrders->each(function ($order) {
            $this->assertEquals(OrderStatus::canceled(), $order->fresh()->status_id);
        });

        $this->assertEquals(4, Order::where('status_id', OrderStatus::canceled())->count());
    }
}
