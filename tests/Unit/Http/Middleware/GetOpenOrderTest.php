<?php

namespace Tests\Unit\Http\Middleware;

use App\Http\Middleware\GetOpenOrder;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GetOpenOrderTest extends TenantTestCase
{
    #[Test]
    public function it_sets_order_on_request_when_user_is_unauthenticated(): void
    {
        $request = new Request;

        $middleware = new GetOpenOrder;

        $middleware->handle($request, function ($req) {
            $this->assertNull($req->order());
            return new Response();
        });
    }

    #[Test]
    public function it_sets_open_order_variable_in_response_for_an_unauthenticated_user(): void
    {
        // assumes store index has this middleware set on it
        $this->call('GET', route('store.index'))
            ->assertViewHas('openOrder', fn($value) => is_null($value));
    }

    #[Test]
    public function it_sets_order_on_request_when_user_is_authenticated(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $expected_order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDay(),
            'blueprint_id' => RecurringOrder::factory()->create(['customer_id' => $user->id])->id,
        ]);

        $request = new Request;

        $middleware = new GetOpenOrder;

        $middleware->handle($request, function ($req) use ($user, $expected_order) {
            $order = $req->order();
            $this->assertInstanceOf(Order::class, $order);
            $this->assertEquals($expected_order->id, $order->id);
            return new Response();
        });
    }

    #[Test]
    public function it_sets_open_order_variable_in_response_for_an_authenticated_user(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $user = User::factory()->create();

        $expected_order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDay(),
            'blueprint_id' => RecurringOrder::factory()->create(['customer_id' => $user->id])->id,
        ]);

        $this->actingAs($user);

        // assumes store index has this middleware set on it
        $this->call('GET', route('store.index'))
            ->assertViewHas(
                'openOrder',
                fn($value) => $value instanceof Order && $value->id === $expected_order->id
            );
    }
}