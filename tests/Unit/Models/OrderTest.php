<?php

namespace Tests\Unit\Models;

use App\Actions\Order\AddItem;
use App\Actions\Order\AddPromoItem;
use App\Actions\Order\AddRecurringItem;
use App\Actions\Order\Cancel;
use App\Actions\Order\Confirm;
use App\Actions\Order\ConfirmRecurring;
use App\Actions\Order\Remove;
use App\Actions\Order\RemovePromoItems;
use App\Actions\Order\SetItemQuantity;
use App\Actions\ProcessOrder;
use App\Events\Order\OrderWasCanceled;
use App\Exceptions\PickupNotFoundException;
use App\Models\Address;
use App\Models\Date;
use App\Models\Event;
use App\Models\GiftCertificate;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PackingGroup;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\ProductType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event as EventFacade;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderTest extends TenantTestCase
{
    use WithFaker;

    #[Test]
    public function it_can_apply_a_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 200]);

        $currentEventCount = Event::count();

        $order->applyCredit(500, 'some apply reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 700
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount + 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => 'some apply reason',
            'event_id' => 'credit_applied',
            'metadata' => json_encode(['amount' => 500])
        ]);
    }

    #[Test]
    public function it_cannot_apply_a_zero_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 100]);

        $currentEventCount = Event::count();

        $order->applyCredit(0, 'some apply reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 100
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount);
    }

    #[Test]
    public function it_cannot_apply_a_negative_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 100]);

        $currentEventCount = Event::count();

        $order->applyCredit(-1, 'some apply reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 100
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount);
    }

    #[Test]
    public function it_can_remove_a_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 500]);

        $currentEventCount = Event::count();

        $order->removeCredit(100, 'some removal reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 400
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount + 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => 'some removal reason',
            'event_id' => 'credit_removed',
            'metadata' => json_encode(['amount' => 100])
        ]);
    }

    #[Test]
    public function it_can_remove_a_credit_larger_than_currently_applied_amount(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 500]);

        $currentEventCount = Event::count();

        $order->removeCredit(1000, 'some removal reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 0
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount + 1);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => 'some removal reason',
            'event_id' => 'credit_removed',
            'metadata' => json_encode(['amount' => 1000])
        ]);
    }

    #[Test]
    public function it_cannot_remove_a_zero_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 500]);

        $currentEventCount = Event::count();

        $order->removeCredit(0, 'some removal reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 500
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount);
    }

    #[Test]
    public function it_cannot_remove_a_negative_credit(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['credit_applied' => 500]);

        $currentEventCount = Event::count();

        $order->removeCredit(-1, 'some removal reason');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 500
        ]);

        $this->assertDatabaseCount(Event::class, $currentEventCount);
    }

    #[Test]
    public function it_can_accept_credit_to_a_confirmed_unpaid_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->make([
            'paid' => false,
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'canceled' => false
        ]);

        $this->assertTrue($order->canAcceptCredit());
    }

    #[Test]
    public function it_cannot_accept_credit_to_a_unconfirmed_unpaid_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->make([
            'paid' => false,
            'confirmed' => false,
            'status_id' => OrderStatus::processing(),
            'canceled' => false
        ]);

        $this->assertFalse($order->canAcceptCredit());
    }

    #[Test]
    public function it_cannot_accept_credit_to_a_confirmed_paid_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->make([
            'paid' => true,
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'canceled' => false
        ]);

        $this->assertFalse($order->canAcceptCredit());
    }

    #[Test]
    public function it_cannot_accept_credit_to_a_confirmed_unpaid_order_when_in_a_canceled_status(): void
    {
        /** @var Order $order */
        $order = Order::factory()->make([
            'paid' => false,
            'confirmed' => true,
            'status_id' => OrderStatus::canceled(),
            'canceled' => false
        ]);

        $this->assertFalse($order->canAcceptCredit());
    }

    #[Test]
    public function it_cannot_accept_credit_to_a_confirmed_unpaid_order_when_canceled_is_true(): void
    {
        /** @var Order $order */
        $order = Order::factory()->make([
            'paid' => false,
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'canceled' => true
        ]);

        $this->assertFalse($order->canAcceptCredit());
    }

    #[Test]
    public function it_cannot_accept_credit_to_an_order_with_gift_cards(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create([
            'paid' => false,
            'confirmed' => true,
            'status_id' => OrderStatus::processing(),
            'canceled' => false
        ]);

        $product = Product::factory()->create(['type_id' =>  ProductType::GIFT_CARD->value]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertFalse($order->canAcceptCredit());
    }

    #[Test]
    public function it_saves_a_confirmed_orders_totals_when_update_totals_is_called(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false, 'subtotal' => 50, 'original_subtotal' => 50]);

        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0
        ]);

        $order->setRelation('items', $items);


        $order->updateTotals();

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'subtotal' => 1000, // (250*2) *2
            'original_subtotal' => 1000
        ]);
    }

    #[Test]
    public function it_saves_an_unconfirmed_orders_totals_when_update_totals_is_called(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true, 'subtotal' => 50, 'original_subtotal' => 50]);

        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0
        ]);

        $order->setRelation('items', $items);


        $order->updateTotals();

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'subtotal' => 1000, // (250*2) *2
            'original_subtotal' => 50
        ]);
    }

    #[Test]
    public function it_saves_a_confirmed_order_items_subtotals_when_update_totals_is_called(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false, 'subtotal' => 50, 'original_subtotal' => 50]);

        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0
        ]);

        $order->setRelation('items', $items);


        $order->updateTotals();

        $items->each(function (OrderItem $item) {
            $this->assertDatabaseHas('order_items', [
                'id' => $item->id,
                'subtotal' => 500, // (250*2)
            ]);
        });
    }

    #[Test]
    public function it_saves_an_unconfirmed_order_items_subtotals_when_update_totals_is_called(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true, 'subtotal' => 50, 'original_subtotal' => 50]);

        $items = OrderItem::factory()->count(2)->create([
            'order_id' => $order->id,
            'qty' => 2,
            'unit_price' => 250,
            'subtotal' => 0
        ]);

        $order->setRelation('items', $items);


        $order->updateTotals();

        $items->each(function (OrderItem $item) {
            $this->assertDatabaseHas('order_items', [
                'id' => $item->id,
                'subtotal' => 500, // (250*2)
            ]);
        });
    }

    #[Test]
    public function it_can_be_processed(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        $expected_result = [
            'payment' => []
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn(Order $arg) => $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->assertEquals($expected_result, $order->process());
    }

    #[Test]
    public function it_updates_order_properties_when_canceling_an_order(): void
    {
        Carbon::setTestNow(now());

        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'confirmed' => false,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'canceled_at' => null,
        ]);

        $order->cancel();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'confirmed' => false,
            'canceled' => true,
            'status_id' => OrderStatus::canceled(),
            'canceled_at' => now(),
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_releases_product_inventory_for_confirmed_orders_when_canceling_an_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 12
        ]);
    }

    #[Test]
    public function it_does_not_release_product_inventory_for_unconfirmed_orders_when_canceling_an_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_releases_bundle_product_inventory_for_confirmed_orders_when_canceling_an_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true,]);

        $bundle_product = Product::factory()->create(['track_inventory' => 'bundle', 'is_bundle' => true, 'inventory' => 10]);
        $products = Product::factory()->count(2)->create(['track_inventory' => 'yes', 'is_bundle' => false, 'inventory' => 10]);

        $bundle_product->bundle()->attach([
            $products->first()->id => ['qty' => 2],
            $products->last()->id => ['qty' => 2],
        ]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $bundle_product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 10
        ]);

        $products->each(function (Product $product) {
            $this->assertDatabaseHas(Product::class, [
                'id' => $product->id,
                'inventory' => 14 // 10 + 2*2 bundle qty
            ]);
        });
    }

    #[Test]
    public function it_does_not_release_bundle_product_inventory_for_unconfirmed_orders_when_canceling_an_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        $bundle_product = Product::factory()->create(['track_inventory' => 'bundle', 'is_bundle' => true, 'inventory' => 10]);
        $products = Product::factory()->count(2)->create(['track_inventory' => 'yes', 'is_bundle' => false, 'inventory' => 10]);

        $bundle_product->bundle()->attach([
            $products->first()->id => ['qty' => 2],
            $products->last()->id => ['qty' => 2],
        ]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $bundle_product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Product::class, [
            'id' => $bundle_product->id,
            'inventory' => 10
        ]);

        $products->each(function (Product $product) {
            $this->assertDatabaseHas(Product::class, [
                'id' => $product->id,
                'inventory' => 10
            ]);
        });
    }

    #[Test]
    public function it_refunds_the_customer_applied_credit_when_canceling_a_confirmed_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['credit' => 0]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'credit_applied' => 100,
        ]);

        $order->cancel();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'credit_applied' => 0
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 0,
            'credit' => 100
        ]);
    }

    #[Test]
    public function it_does_not_refund_the_customer_applied_credit_when_canceling_an_unconfirmed_order(): void
    {
        Carbon::setTestNow(now());

        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['credit' => 0]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => false,
            'credit_applied' => 100,
        ]);

        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'credit_applied' => 100
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'credit' => 0
        ]);
    }

    #[Test]
    public function it_does_not_decrement_user_order_count_when_canceling_an_unconfirmed_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['order_count' => 1]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => false,
            'credit_applied' => 100,
        ]);

        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $order->cancel();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'credit_applied' => 100
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'credit' => 0
        ]);
    }

    #[Test]
    public function it_decrements_user_order_count_when_canceling_a_confirmed_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['order_count' => 1]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'is_recurring' => false
        ]);

        $order->cancel();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 0,
        ]);
    }

    #[Test]
    public function it_decrements_user_recurring_order_count_when_canceling_a_confirmed_recurring_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['order_count' => 1, 'recurring_order_count' => 1]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'is_recurring' => true,
            'blueprint_id' => RecurringOrder::factory(),
        ]);

        $order->cancel();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 1,
            'recurring_order_count' => 0
        ]);
    }

    #[Test]
    public function it_does_not_decrements_user_recurring_order_count_when_canceling_an_unconfirmed_recurring_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['order_count' => 1, 'recurring_order_count' => 1]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => false,
            'is_recurring' => true,
        ]);

        $order->cancel();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_count' => 1,
            'recurring_order_count' => 1
        ]);
    }

    #[Test]
    public function it_does_not_sync_blueprint_items_when_cancelling_an_unconfirmed_recurring_order(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        $user = User::factory()->create(['order_count' => 1, 'recurring_order_count' => 1]);

        $blueprint = RecurringOrder::factory()->create();
        $old_blueprint_items = RecurringOrderItem::factory()->count(2)->create(['order_id' => $blueprint->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => false,
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
        ]);

        $order_items = OrderItem::factory()->count(2)->create(['order_id' => $order->id, 'type' => 'standard']);

        $order->cancel();

        foreach ($old_blueprint_items as $old_item) {
            $this->assertDatabaseHas(RecurringOrderItem::class, [
                'id' => $old_item->id,
                'deleted_at' => null
            ]);
        }

        foreach ($order_items as $item) {
            $this->assertDatabaseMissing(RecurringOrderItem::class, [
                'order_id' => $blueprint->id,
                'product_id' => $item->product_id,
            ]);
        }
    }

    #[Test]
    public function it_fires_the_expected_event_upon_cancellation(): void
    {
        EventFacade::fake([OrderWasCanceled::class]);

        /** @var Order $order */
        $order = Order::factory()->create();

        $order->cancel();

        EventFacade::assertDispatched(
            OrderWasCanceled::class,
            function (OrderWasCanceled $event) use ($order) {
                return $event->order->id === $order->id;
            }
        );
    }

    #[Test]
    public function it_can_skip_days(): void
    {
        Carbon::setTestNow(now());

        EventFacade::fake([OrderWasCanceled::class]);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'],['value' => 11]);

        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'pickup_date' => today()->addDays(21),
            'order_end_date' => today()->addDays(19)
        ]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $blueprint = RecurringOrder::factory()->create([
            'ready_at' => null,
            'generate_at' => null,
            'skip_count' => 5,
            'reorder_frequency' => 7,
            'fulfillment_id' => $pickup->id
        ]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'is_recurring' => true,
            'blueprint_id' => $blueprint->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today(),
            'customer_id' => $blueprint->customer_id
        ]);

        $order->skipDays(19);

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'id' => $blueprint->id,
            'ready_at' => $date->pickup_date->setTime(0,0,0)->format('Y-m-d H:i:s'),
            'generate_at' => $date->order_end_date->subDays(3)->setTime(11,0,0)->format('Y-m-d H:i:s'),
            'skip_count' => 6,
        ]);

        $this->assertFalse($blueprint->orders()->where('id', '>', $order->id)->exists());

        EventFacade::assertNotDispatched(
            OrderWasCanceled::class,
            function (OrderWasCanceled $event) use ($order) {
                return $event->order->id === $order->id;
            }
        );

        $this->assertDatabaseHas(User::class, [
            'id' => $blueprint->customer_id,
            'order_skip_count' => 1
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_creates_an_order_with_default_params_for_a_user_who_has_an_associated_pickup(): void
    {
        $pickup = Pickup::factory()->create();
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $currentOrderCount = Order::count();

        Order::createForUser($user);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'type_id' => 1,
            'payment_id' => 0,
            'payment_source_id' => null,
            'schedule_id' => $pickup->schedule_id,
            'pickup_id' => $user->pickup_point,
            'date_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'original_pickup_date' => null,
            'customer_first_name' => $user->first_name,
            'customer_last_name' => $user->last_name,
            'customer_phone' => $user->phone,
            'customer_email' => $user->email,
            'shipping_street' => $user->street,
            'shipping_street_2' => $user->street_2,
            'shipping_city' => $user->city,
            'shipping_state' => $user->state,
            'shipping_zip' => $user->zip,
            'billing_street' => $user->billing_street,
            'billing_street_2' => $user->billing_street_2,
            'billing_city' => $user->billing_city,
            'billing_state' => $user->billing_state,
            'billing_zip' => $user->billing_zip,
            'first_time_order' => true,
            'confirmed' => false,
            'packed' => false,
            'paid' => false,
            'status_id' => 1
        ]);

        $this->assertDatabaseCount('orders', $currentOrderCount + 1);
    }

    #[Test]
    public function it_creates_an_order_with_passed_in_params(): void
    {
        $pickup = Pickup::factory()->create(['schedule_id' => Schedule::factory()->hasDate()]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $params = [
            'type_id' => 2,
            'payment_id' => 1,
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
        ];

        $currentOrderCount = Order::count();

        Order::createForUser($user, $params);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'type_id' => $params['type_id'],
            'payment_id' => $params['payment_id'],
            'deadline_date' => $params['deadline_date'],
            'pickup_date' => $params['pickup_date'],
            'original_pickup_date' => $params['pickup_date'],
        ]);
    }

    #[Test]
    public function it_creates_an_order_with_pickup_point_settings(): void
    {
        $pickup = Pickup::factory()->create([
            'settings' => [
                'sales_channel' => 3
            ]
        ]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $currentOrderCount = Order::count();

        Order::createForUser($user);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'type_id' => 3,
        ]);
    }

    #[Test]
    public function it_creates_an_order_with_user_settings(): void
    {
        $user = User::factory()->create([
            'settings' => [
                'default_payment_method' => 2
            ]
        ]);

        $currentOrderCount = Order::count();

        Order::createForUser($user);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'payment_id' => 2,
        ]);
    }

    #[Test]
    public function it_does_not_created_a_recurring_order_when_user_has_a_blueprint(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => 2]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);
        RecurringOrder::factory()->create(['customer_id' => $user->id]);

        $currentOrderCount = Order::count();

        Order::createForUser($user);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'is_recurring' => null,
            'blueprint_id' => null
        ]);
    }

    #[Test]
    public function it_creates_an_order_with_the_pickup_open_order_window(): void
    {
        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create(['schedule_id' => $schedule->id]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $currentOrderCount = Order::count();

        Order::createForUser($user);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'date_id' => $date->id,
            'deadline_date' => $date->order_end_date->format('Y-m-d'),
            'pickup_date' => $date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $date->pickup_date->format('Y-m-d'),
        ]);
    }

    #[Test]
    public function it_creates_an_order_with_passed_in_params_that_override_the_pickup_open_order_window(): void
    {
        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create(['schedule_id' => $schedule->id]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create(['pickup_point' => $pickup->id]);

        $params = [
            'deadline_date' => now()->addDay()->format('Y-m-d'),
            'pickup_date' => now()->addDays(2)->format('Y-m-d'),
        ];

        $currentOrderCount = Order::count();

        Order::createForUser($user, $params);

        $this->assertDatabaseCount(Order::class, $currentOrderCount + 1);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $user->id,
            'date_id' => null,
            'deadline_date' => $params['deadline_date'],
            'pickup_date' => $params['pickup_date'],
            'original_pickup_date' => $params['pickup_date'],
        ]);
    }

    #[Test]
    public function it_throws_exception_when_creating_order_for_a_user_with_no_associated_pickup(): void
    {
        $user = User::factory()->create(['pickup_point' => 0]);

        $this->expectException(PickupNotFoundException::class);

        Order::createForUser($user);

        $this->assertDatabaseCount('orders', 0);
    }

    #[Test]
    public function it_can_fetch_the_deadline_datetime_without_a_custom_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineDatetime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => null]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_datetime_with_a_midnight_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 24]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_datetime_with_a_custom_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 8]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->setTime(8, 0), $order->deadlineDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_cannot_fetch_the_deadline_datetime_when_there_is_not_set_deadline_date(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 8]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => null
        ]);

        $this->assertNull($order->deadlineDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_cannot_fetch_the_deadline_reminder_datetime_when_there_is_no_deadline(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => null
        ]);

        $this->assertNull($order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_cannot_fetch_the_deadline_reminder_datetime_when_there_is_no_blueprint_schedule(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => null
        ]);

        $this->assertNull($order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_cannot_fetch_the_deadline_reminder_datetime_when_the_schedule_reminder_is_disabled(): void
    {
        Carbon::setTestNow(now());

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => false]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => null
        ]);

        $this->assertNull($order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_reminder_datetime_without_a_custom_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineReminderDatetime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => null]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_reminder_datetime_with_a_midnight_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 24]);

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->endOfDay(), $order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_reminder_datetime_with_a_custom_deadline_hour(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 8]);

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->setTime(8, 0), $order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_reminder_datetime_with_a_custom_schedule_hours_before_and_days_before(): void
    {
        Carbon::setTestNow(now());

        config(['tenant.settings.order_deadline_hour' => 8]);

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create([
            'subscription_reminder_enabled' => true,
            'subscription_reminder_hour' => 5,
            'subscription_reminder_days' => 1,
        ]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals($order->deadlineDatetime()->subHours(24 + 5), $order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_deadline_reminder_datetime_with_a_global_hours_before_and_days_before(): void
    {
        Carbon::setTestNow(now());

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create([
            'subscription_reminder_enabled' => null,
            'subscription_reminder_hour' => null,
            'subscription_reminder_days' => null,
        ]);

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_days_before'], ['value' => 2]);
        Setting::updateOrCreate(['key' => 'recurring_orders_deadline_hours_before'], ['value' => 3]);

        /** @var RecurringOrder $blueprint */
        $blueprint = RecurringOrder::factory()->create(['schedule_id' => $schedule->id]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals($order->deadlineDatetime()->subHours(48 + 3), $order->deadlineReminderDatetime());

        Carbon::setTestNow();
    }


    #[Test]
    public function it_cannot_fetch_the_inventory_timing_datetime_when_there_is_no_deadline(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => null
        ]);

        $this->assertNull($order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_without_a_custom_deadline_hour_or_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDay(3)->endOfDay(), $order->inventoryTimingDatetime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => null]);
        $this->assertEquals(today()->subDay(3)->endOfDay(), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_with_a_midnight_deadline_hour_and_no_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 24]);

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDay(3)->endOfDay(), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_with_a_custom_deadline_hour_and_no_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 8]);

        /** @var Schedule $schedule */
        $schedule = Schedule::factory()->create(['subscription_reminder_enabled' => true]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDay(3)->setTime(8, 0), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_without_a_custom_deadline_hour_but_with_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        $inventory_timing_days = 3;

        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'], ['value'  => $inventory_timing_days]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDays($inventory_timing_days)->endOfDay(), $order->inventoryTimingDatetime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => null]);

        $this->assertEquals(today()->subDays($inventory_timing_days)->endOfDay(), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_with_a_midnight_deadline_hour_and_with_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 24]);

        $inventory_timing_days = 3;

        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'], ['value'  => $inventory_timing_days]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDays($inventory_timing_days)->endOfDay(), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_fetch_the_inventory_timing_datetime_with_a_custom_deadline_hour_and_with_inventory_timing_setting(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value'  => 8]);

        $inventory_timing_days = 3;

        Setting::updateOrCreate(['key' => 'recurring_orders_inventory_timing'], ['value'  => $inventory_timing_days]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'pickup_date' => today()->addDay(),
            'deadline_date' => today()
        ]);

        $this->assertEquals(today()->subDays($inventory_timing_days)->setTime(8, 0), $order->inventoryTimingDatetime());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_routes_mail_notifications_to_the_order_email_address_when_present(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_email' => '<EMAIL>'
        ]);
        $this->assertEquals('<EMAIL>', $order->routeNotificationForMail());
    }

    #[Test]
    public function it_routes_mail_notifications_to_the_customer_email_address_when_order_email_not_present(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_email' => ''
        ]);
        $this->assertEquals('<EMAIL>', $order->routeNotificationForMail());
    }

    #[Test]
    public function it_routes_twilio_notifications_to_the_order_phone_when_present(): void
    {
        $user = User::factory()->create(['email' => '************']);
        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => '************'
        ]);
        $this->assertEquals('+12025550193', $order->routeNotificationForTwilio());
    }

    #[Test]
    public function it_routes_twilio_notifications_to_the_customer_phone_when_order_phone_not_present(): void
    {
        $user = User::factory()->create(['phone' => '************']);
        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'customer_phone' => ''
        ]);
        $this->assertEquals('+12025550193', $order->routeNotificationForTwilio());
    }

    #[Test]
    public function it_returns_null_for_the_order_deadline_end_time_if_no_deadline_date_is_set(): void
    {
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);

        /** @var Order $order */
        $order = Order::factory()->create(['deadline_date' => null]);

        $this->assertNull($order->deadlineEndTime());
    }

    #[Test]
    public function it_returns_one_minute_before_midnight_if_order_deadline_hour_is_24(): void
    {
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);

        /** @var Order $order */
        $order = Order::factory()->create();

        $this->assertEquals('11:59 PM', $order->deadlineEndTime());
    }

    #[Test]
    public function it_returns_the_formatted_deadline_end_for_the_order(): void
    {
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 10]);

        /** @var Order $order */
        $order = Order::factory()->create();

        $this->assertEquals('10:00 AM', $order->deadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 12]);

        /** @var Order $order */
        $order = Order::factory()->create();

        $this->assertEquals('12:00 PM', $order->deadlineEndTime());
    }

    #[Test]
    public function it_can_scope_to_unconfirmed_orders(): void
    {
        $confirmed = Order::factory()->create(['confirmed' => true]);
        $unconfirmed = Order::factory()->create(['confirmed' => false]);

        $orders = Order::unconfirmed()->pluck('id');
        $this->assertFalse($orders->contains($confirmed->id));
        $this->assertTrue($orders->contains($unconfirmed->id));
    }

    #[Test]
    public function it_can_get_its_free_items(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'subtotal' => 1,
            'type' => 'promo',
        ]);

        $this->assertEmpty($order->getFreeItems());

        /** @var Order $order */
        $order = Order::factory()->create();
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'subtotal' => 0,
            'type' => 'standard',
        ]);

        $this->assertEmpty($order->getFreeItems());

        /** @var Order $order */
        $order = Order::factory()->create();
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'subtotal' => 0,
            'type' => 'promo',
        ]);

        $this->assertNotEmpty($order->getFreeItems());
    }

    #[Test]
    public function it_can_scope_to_undecided_recurring_carts(): void
    {
        $confirmed = Order::factory()->create(['confirmed' => true, 'is_recurring' => true, 'blueprint_id' => null]);
        $unconfirmed = Order::factory()->create(['confirmed' => false, 'is_recurring' => true, 'blueprint_id' => null]);
        $on_blueprint = Order::factory()->create(['confirmed' => false, 'is_recurring' => true, 'blueprint_id' => RecurringOrder::factory()]);

        $order_ids = Order::undecidedRecurringCart()->pluck('id');

        $this->assertFalse($order_ids->contains($confirmed->id));
        $this->assertTrue($order_ids->contains($unconfirmed->id));
        $this->assertFalse($order_ids->contains($on_blueprint->id));
    }

    #[Test]
    public function it_knows_if_it_is_the_customers_first_order(): void
    {
        $customer = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        $this->assertTrue($order->isCustomersFirst());

        $order = Order::factory()->create(['customer_id' => $customer->id]);
        $this->assertFalse($order->isCustomersFirst());
    }

    #[Test]
    public function it_can_determine_when_order_meets_free_delivery(): void
    {
        // no pickup
        $order = Order::factory()->create(['pickup_id' => 0, 'subtotal' => 12300]);
        $this->assertFalse($order->meetsFreeDelivery());

        // does not cap delivery fee (apply limit)
        $pickup = Pickup::factory()->create(['apply_limit' => false, 'display_cart_shipping_calculator' => true, 'delivery_total_threshold' => 123, 'delivery_fee_cap' => 0]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'subtotal' => 12300]);
        $this->assertFalse($order->meetsFreeDelivery());

        // has a delivery cap
        $pickup = Pickup::factory()->create(['apply_limit' => true, 'display_cart_shipping_calculator' => true, 'delivery_total_threshold' => 123, 'delivery_fee_cap' => 12]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'subtotal' => 12300]);
        $this->assertFalse($order->meetsFreeDelivery());

        // does not meet free delivery threshold
        $pickup = Pickup::factory()->create(['apply_limit' => true, 'display_cart_shipping_calculator' => true, 'delivery_total_threshold' => 123, 'delivery_fee_cap' => 0]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'subtotal' => 12200]);
        $this->assertFalse($order->meetsFreeDelivery());

        // has pickup, applies limit, no delivery cap, shows calculator, meets free delivery threshold
        $pickup = Pickup::factory()->create(['apply_limit' => true, 'display_cart_shipping_calculator' => true, 'delivery_total_threshold' => 123, 'delivery_fee_cap' => 0]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'subtotal' => 12300]);
        $this->assertTrue($order->meetsFreeDelivery());
    }

    #[Test]
    public function it_can_determine_the_delivery_fee(): void
    {
        // meets free delivery
        $pickup = Pickup::factory()->create(['apply_limit' => true, 'display_cart_shipping_calculator' => true, 'delivery_total_threshold' => 123, 'delivery_fee_cap' => 0]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'subtotal' => 12300]);
        $this->assertEquals(0, $order->deliveryFee());

        // unconfirmed, uses pickup fixed rate
        $pickup = Pickup::factory()->create(['delivery_rate' => 222, 'settings' => ['delivery_fee_type' => 2]]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'subtotal' => 12300, 'delivery_rate'=> 111, 'delivery_fee_type' => 1, 'weight' => 10]);
        $this->assertEquals(22200, $order->deliveryFee());

        // unconfirmed, uses pickup variable weighted rate
        $pickup = Pickup::factory()->create(['delivery_rate' => 222, 'settings' => ['delivery_fee_type' => 1]]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'subtotal' => 12300, 'delivery_rate'=> 111, 'delivery_fee_type' => 1, 'weight' => 10]);
        $this->assertEquals(222000, $order->deliveryFee());

        // confirmed, uses pickup fixed rate
        $pickup = Pickup::factory()->create(['delivery_rate' => 222, 'settings' => ['delivery_fee_type' => 1]]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'subtotal' => 12300, 'delivery_rate'=> 111, 'delivery_fee_type' => 2, 'weight' => 10]);
        $this->assertEquals(111, $order->deliveryFee());

        // confirmed, uses pickup variable weighted rate
        $pickup = Pickup::factory()->create(['delivery_rate' => 222, 'settings' => ['delivery_fee_type' => 2]]);
        $order = Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'subtotal' => 12300, 'delivery_rate'=> 111, 'delivery_fee_type' => 1, 'weight' => 10]);
        $this->assertEquals(1110, $order->deliveryFee());
    }

    #[Test]
    public function it_returns_null_next_recurring_order_date_when_order_is_not_on_blueprint(): void
    {
        $this->assertNull(
            Order::factory()->make(['blueprint_id' => null])
                ->nextRecurringOrderPickupDate()
        );
    }

    #[Test]
    public function it_returns_next_recurring_order_date_when_order_is_on_blueprint(): void
    {
        \Illuminate\Support\Carbon::setTestNow(now());

        $blueprint = RecurringOrder::factory()->make(['reorder_frequency' => 14]);
        $order = Order::factory()->make(['pickup_date' => now()->addDays(2)]);
        $order->setRelation('blueprint', $blueprint);

        $this->assertEquals(now()->addDays(16)->format('Y-m-d'), $order->nextRecurringOrderPickupDate()->format('Y-m-d'));

        \Illuminate\Support\Carbon::setTestNow();
    }

    #[Test]
    public function it_knows_if_it_can_be_modified(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'],['value' => false]);
        /** @var Order $newOrder */
        $newOrder = Order::factory()->make(['status_id' => OrderStatus::confirmed()]);

        $this->assertTrue($newOrder->canBeModified());

        $completedOrder = Order::factory()->make(['status_id' => OrderStatus::completed()]);

        $this->assertFalse($completedOrder->canBeModified());

        $unconfirmedOrder = Order::factory()->make(['confirmed' => false, 'status_id' => OrderStatus::confirmed()]);

        $this->assertTrue($unconfirmedOrder->canBeModified());

        $confirmedOrderWithoutDeadline = Order::factory()->make([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => null,
            'pickup_date' => now()->addDays(2),
        ]);

        $this->assertFalse($confirmedOrderWithoutDeadline->canBeModified());

        $confirmedOrderWithoutPickup = Order::factory()->make([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => now()->addDays(2),
            'pickup_date' => null,
        ]);

        $this->assertFalse($confirmedOrderWithoutPickup->canBeModified());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $confirmedOrder = Order::factory()->make([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => now()->addDays(2),
            'pickup_date' => now()->addDays(2),
        ]);

        $this->assertFalse($confirmedOrder->canBeModified());

        $confirmedOrder = Order::factory()->make([
            'confirmed' => true,
            'blueprint_id' => RecurringOrder::factory(),
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => now()->addDays(2),
            'pickup_date' => now()->addDays(2),
        ]);

        $this->assertTrue($confirmedOrder->canBeModified());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        $this->assertTrue($confirmedOrder->canBeModified());

        $pastOrder = Order::factory()->make([
            'confirmed' => true,
            'status_id' => OrderStatus::confirmed(),
            'deadline_date' => now()->subDays(2),
            'pickup_date' => now()->subDays(1),
        ]);

        $this->assertFalse($pastOrder->canBeModified());
    }

    #[Test]
    public function it_can_filter_down_to_orders_confirmed_between_two_dates(): void
    {
        Carbon::setTestNow(now());

        $non_confirmed = Order::factory()->create(['canceled' => false, 'confirmed_date' => null]);
        $confirmed_before = Order::factory()->create(['canceled' => false, 'confirmed_date' => today()->subDays(2)]);
        $confirmed_one = Order::factory()->create(['canceled' => false, 'confirmed_date' => today()->subDay()]);
        $confirmed_two = Order::factory()->create(['canceled' => false, 'confirmed_date' => today()]);
        $confirmed_three = Order::factory()->create(['canceled' => false, 'confirmed_date' => today()->addDay()]);
        $confirmed_after = Order::factory()->create(['canceled' => false, 'confirmed_date' => today()->addDays(2)]);

        $orders = Order::confirmedBetween(today()->subDay(), today()->addDay())->pluck('id');

        $this->assertFalse($orders->contains($non_confirmed->id));
        $this->assertFalse($orders->contains($confirmed_before->id));
        $this->assertTrue($orders->contains($confirmed_one->id));
        $this->assertTrue($orders->contains($confirmed_two->id));
        $this->assertTrue($orders->contains($confirmed_three->id));
        $this->assertFalse($orders->contains($confirmed_after->id));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_remove_an_item(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create();

        $item->order->removeItem($item);

        $this->assertDatabaseMissing(OrderItem::class, ['id' => $item->id]);
    }

    #[Test]
    public function it_removes_issued_codes_when_removing_a_gift_card_item(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['product_id' => $product->id]);
        $other_item = OrderItem::factory()->create(['product_id' => $product->id]);

        $code_one = GiftCertificate::factory()->create(['product_id' => $product->id, 'order_item_id' => $other_item->id]);
        $code_two = GiftCertificate::factory()->create(['product_id' => $product->id, 'order_item_id' => $item->id]);

        $item->order->removeItem($item);

        $this->assertDatabaseHas(GiftCertificate::class, ['id' => $code_one->id]);
        $this->assertDatabaseMissing(GiftCertificate::class, ['id' => $code_two->id]);
    }

    #[Test]
    public function it_does_not_increment_inventory_when_removing_an_item_from_an_unconfirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $order->removeItem($item);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_increments_inventory_when_removing_an_item_from_a_confirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id, 'qty' => 2]);

        $this->actingAsAdmin();

        $order->removeItem($item);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 12
        ]);
    }

    #[Test]
    public function it_does_not_record_an_event_when_removing_an_item_from_an_unconfirmed_order(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => false]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2]);

        $order->removeItem($item);

        $this->assertDatabaseMissing(Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_records_an_event_when_removing_an_item_from_a_confirmed_order(): void
    {
        Carbon::setTestNow(now());

        /** @var Order $order */
        $order = Order::factory()->create(['confirmed' => true]);

        /** @var OrderItem $item */
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2]);

        $this->actingAsAdmin();

        $order->removeItem($item);

        $this->assertDatabaseHas(Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "{$item->title} was removed from the order",
            'event_id' => 'order_item_removed',
            'user_id' => auth()->id(),
            'created_at' => now()->format('Y-m-d H:i:s'),
            'metadata' => json_encode([
                'item_id' => $item->id,
                'qty' => 2
            ])
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_add_an_item(): void
    {
        $product = Product::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create();

        $this->mock(AddItem::class, function (MockInterface $mock) use ($order, $product) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    Mockery::on(fn (Product $arg) => $arg->id === $product->id),
                    2,
                    'some_type',
                    false
                )
                ->andReturn(new OrderItem);
        });

        $order->addItem($product, 2, 'some_type');
    }

    #[Test]
    public function it_can_add_a_recurring_item(): void
    {
        $item = RecurringOrderItem::factory()->create();

        $order = Order::factory()->create();

        $this->mock(AddRecurringItem::class, function (MockInterface $mock) use ($order, $item) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    Mockery::on(fn (RecurringOrderItem $arg) => $arg->id === $item->id),
                )
                ->andReturn(new OrderItem);
        });

        $order->addRecurringItem($item);
    }

    #[Test]
    public function it_can_add_update_item_quantity(): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::factory()->create();

        $this->mock(SetItemQuantity::class, function (MockInterface $mock) use ($item) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $item->order_id),
                    Mockery::on(fn (OrderItem $arg) => $arg->id === $item->id),
                    2,
                    false
                )
                ->andReturn(new OrderItem);
        });

        $item->order->updateItemQuantity($item, 2);
    }


    #[Test]
    public function it_can_change_its_purchase_type(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create(['is_recurring' => null]);

        $order->changePurchaseType('unknown');

        $this->assertFalse($order->isRecurring());

        $this->mock(AddPromoItem::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    null
                )
                ->andReturn($order);
        });

        $result = $order->changePurchaseType('recurring');
        $this->assertTrue($result->isRecurring());

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => true
        ]);

        $this->mock(RemovePromoItems::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn (Order $arg) => $arg->id === $order->id))
                ->andReturn($order);
        });

        $result = $order->changePurchaseType('one_time_purchase');
        $this->assertFalse($result->isRecurring());

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => null
        ]);
    }

    #[Test]
    public function it_doesnt_confirm_an_already_confirmed_order(): void
    {
        $order = Order::factory(['confirmed' => true])->make();

        $this->mock(Confirm::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $order->confirm();
    }

    #[Test]
    public function it_confirms_a_non_blueprint_order(): void
    {
        $order = Order::factory(['confirmed' => false, 'is_recurring' => true, 'blueprint_id' => null])->create();

        $params = ['foo' => 'bar'];

        $this->mock(Confirm::class, function (MockInterface $mock) use ($params, $order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    $params
                )
                ->andReturn($order);
        });

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $order->confirm($params);
    }

    #[Test]
    public function it_confirms_a_blueprint_order(): void
    {
        $blueprint = RecurringOrder::factory()->create();
        $order = Order::factory(['confirmed' => false, 'is_recurring' => false, 'blueprint_id' => $blueprint->id])->create();
        $params = ['foo' => 'bar'];

        $this->mock(Confirm::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) use ($params, $order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                    $params
                )
                ->andReturn($order);
        });

        $order->confirm($params);
    }

    #[Test]
    public function it_can_get_its_recurring_order_savings_when_order_is_not_recurring(): void
    {
        $order = Order::factory()->create(['is_recurring' => false]);

        $this->assertEquals(0, $order->getRecurringOrderSavings());
    }

    #[Test]
    public function it_can_get_its_recurring_order_savings(): void
    {
        $order = Order::factory()->create(['is_recurring' => true]);
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 3333]);
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 6666]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldNotReceive('excludedProductIds');
        });

        // (9999) * 0.05 rounded
        $this->assertEquals(500, $order->getRecurringOrderSavings());
    }

    #[Test]
    public function its_recurring_order_savings_does_not_include_gift_card_products(): void
    {
        $order = Order::factory()->create(['is_recurring' => true]);
        $product_one = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $product_two = Product::factory()->create();
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 3333, 'product_id' => $product_one->id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 6666, 'product_id' => $product_two->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldNotReceive('excludedProductIds');
        });

        // (6666) * 0.05 rounded
        $this->assertEquals(333, $order->getRecurringOrderSavings());
    }

    #[Test]
    public function its_gets_the_recurring_orders_savings_when_the_order_is_paid(): void
    {
        $order = Order::factory()->create(['is_recurring' => true, 'subscription_savings' => 120, 'paid' => true]);
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 3333]);
        OrderItem::factory()->create(['order_id' => $order->id, 'subtotal' => 6666]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
        });

        $this->assertEquals(120, $order->getRecurringOrderSavings());
    }

    #[Test]
    public function it_can_fetch_its_subscription_items(): void
    {
        $subscription_product = Product::factory()->create();
        $one_time_product = Product::factory()->create();

        $order = Order::factory()->create();
        $subscription_item = OrderItem::factory()->for($order)->create(['product_id' => $subscription_product->id]);
        $one_time_item = OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $subscription_items = $order->subscriptionItems();
        $this->assertTrue($subscription_items->contains(fn(OrderItem $item) => $item->id === $subscription_item->id));
        $this->assertTrue($subscription_items->doesntContain(fn(OrderItem $item) => $item->id === $one_time_item->id));
    }

    #[Test]
    public function it_can_fetch_its_one_time_items(): void
    {
        $subscription_product = Product::factory()->create();
        $one_time_product = Product::factory()->create();

        $order = Order::factory()->create();
        $subscription_item = OrderItem::factory()->for($order)->create(['product_id' => $subscription_product->id]);
        $one_time_item = OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $one_time_items = $order->oneTimeItems();
        $this->assertTrue($one_time_items->contains(fn(OrderItem $item) => $item->id === $one_time_item->id));
        $this->assertTrue($one_time_items->doesntContain(fn(OrderItem $item) => $item->id === $subscription_item->id));
    }

    #[Test]
    public function it_can_determine_if_it_has_subscription_eligible_items(): void
    {
        $one_time_product = Product::factory()->create();

        $order = Order::factory()->create();
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($order->hasSubscriptionEligibleItems());

        OrderItem::factory()->for($order)->create();

        $order->refresh();

        $this->assertTrue($order->hasSubscriptionEligibleItems());
    }

    #[Test]
    public function it_is_eligible_for_subscription_when_order_is_on_blueprint(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup, 'blueprint_id' => RecurringOrder::factory()]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);
        OrderItem::factory()->for($order)->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldNotReceive('excludedProductIds');
        });

        $this->assertTrue($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_order_only_has_excluded_items(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_customer_is_already_on_subscription(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);
        OrderItem::factory()->for($order)->create();

        RecurringOrder::factory()->create(['customer_id' => $user->id]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_schedule_is_not_repeating(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_CUSTOM, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);
        OrderItem::factory()->for($order)->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_is_not_eligible_for_subscription_when_schedule_has_no_frequency_options(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => []]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);
        OrderItem::factory()->for($order)->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertFalse($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_determines_when_order_is_eligible_for_subscription(): void
    {
        $one_time_product = Product::factory()->create();

        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $user = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup]);
        OrderItem::factory()->for($order)->create(['product_id' => $one_time_product->id]);
        OrderItem::factory()->for($order)->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($one_time_product) {
            $mock->shouldReceive('excludedProductIds')->andReturn(collect([$one_time_product->id]));
        });

        $this->assertTrue($order->cartIsEligibleForSubscription());
    }

    #[Test]
    public function it_can_scope_to_orders_ready_to_auto_confirm(): void
    {
        $today = today();

        Carbon::setTestNow($today);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($today) {
            $mock->shouldReceive('autoConfirmationDeadlineDate')->once()->withNoArgs()->andReturn($today);
        });

        $one_time_order = Order::factory()->create([
            'blueprint_id' => null,
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        $confirmed_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => true,
            'canceled' => false,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        $canceled_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => true,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        $deadline_not_hit_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today->copy()->addDay(),
            'pickup_date' => $today->copy()->addDays(2)
        ]);

        $pickup_in_past_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today->copy()->subDays(2),
            'pickup_date' => $today->copy()->subDay()
        ]);

        $deadline_today_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today,
            'pickup_date' => $today->copy()->addDay()
        ]);

        $deadline_in_past_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'confirmed' => false,
            'canceled' => false,
            'deadline_date' => $today->copy()->subDay(),
            'pickup_date' => $today->copy()->addDay()
        ]);

        $orders = Order::readyForAutoConfirmation()->pluck('id');

        $this->assertTrue($orders->doesntContain(fn(int $order_id) => $order_id === $one_time_order->id));
        $this->assertTrue($orders->doesntContain(fn(int $order_id) => $order_id === $confirmed_order->id));
        $this->assertTrue($orders->doesntContain(fn(int $order_id) => $order_id === $canceled_order->id));
        $this->assertTrue($orders->doesntContain(fn(int $order_id) => $order_id === $deadline_not_hit_order->id));
        $this->assertTrue($orders->doesntContain(fn(int $order_id) => $order_id === $pickup_in_past_order->id));
        $this->assertTrue($orders->contains(fn(int $order_id) => $order_id === $deadline_today_order->id));
        $this->assertTrue($orders->contains(fn(int $order_id) => $order_id === $deadline_in_past_order->id));
    }

    #[Test]
    public function it_can_determine_if_it_is_fulfilled_virtually(): void
    {
        $order = Order::factory()->create();

        $this->assertFalse($order->isFulfilledVirtually());

        $product = Product::factory()->create(['type_id' => ProductType::PREORDER->value, 'settings' => ['fulfilment_method' => 'virtual']]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertTrue($order->isFulfilledVirtually());

        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value, 'settings' => ['fulfilment_method' => 'virtual']]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertTrue($order->isFulfilledVirtually());
    }

    #[Test]
    public function it_can_determine_if_it_only_has_virtual_gift_card(): void
    {
        $order = Order::factory()->create();

        $this->assertFalse($order->hasOnlyVirtualGiftCards());

        $product = Product::factory()->create(['type_id' => ProductType::PREORDER->value, 'settings' => ['fulfilment_method' => 'virtual']]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertFalse($order->hasOnlyVirtualGiftCards());

        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value, 'settings' => ['fulfilment_method' => 'virtual']]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->assertTrue($order->hasOnlyVirtualGiftCards());
    }

    #[Test]
    public function it_can_calculate_its_total_due(): void
    {
        $order = Order::factory()->make(['total' => 1234, 'payments_subtotal' => 234, 'paid' => false]);
        $this->assertEquals(1000, $order->total_due);

        $order->paid = true;
        $this->assertEquals(0, $order->total_due);
    }

    public function it_can_scope_orders_since_a_given_date()
    {
        Carbon::setTestNow(now());
        Order::factory(2)->create(['confirmed_date' => today()->subDays(20)]);
        Order::factory(3)->create(['confirmed_date' => today()->subDays(40)]);
        Order::factory(4)->create(['confirmed_date' => today()->subDays(70)]);

        $orders30days = Order::since(today()->subDays(30))->count();
        $this->assertEquals(2, $orders30days);
        $orders30days = Order::since(today()->subDays(60))->count();
        $this->assertEquals(5, $orders30days);
        $orders30days = Order::since(today()->subDays(90))->count();
        $this->assertEquals(9, $orders30days);
        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_be_canceled(): void
    {
        $order = Order::factory()->create();

        $this->mock(Cancel::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')->once()->with(Mockery::on(fn(Order $arg) => $arg->id === $order->id))->andReturn($order);
        });

        $order->cancel();
    }

    #[Test]
    public function it_can_be_removed(): void
    {
        $order = Order::factory()->create();

        $this->mock(Remove::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')->once()->with(Mockery::on(fn(Order $arg) => $arg->id === $order->id))->andReturnUndefined();
        });

        $order->remove();
    }

    #[Test]
    public function it_knows_if_a_billing_method_has_been_selected_when_delivery_method_has_limited_options(): void
    {
        $enabled_payments = Payment::factory(2)->create(['enabled' => true]);
        $disabled_payments = Payment::factory(2)->create(['enabled' => false]);

        $delivery_method = Pickup::factory()->create([
            'payment_methods' => [$enabled_payments[0]->id, $disabled_payments[0]->id]
        ]);

        $cart = Order::factory()->create(['payment_id' => 0]);
        $cart->updateCartLocation($delivery_method);

        // not set
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, enabled, and allowed by location
        $cart->setBillingInfo(['method' => $enabled_payments[0]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set, enabled, but not allowed by location
        $cart = $cart->setBillingInfo(['method' => $enabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, allowed by location, but globally disabled
        $cart = $cart->setBillingInfo(['method' => $disabled_payments[0]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set, disabled, and not allowed by location
        $cart = $cart->setBillingInfo(['method' => $disabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());
    }

    #[Test]
    public function it_knows_if_a_billing_method_has_been_selected_when_delivery_method_uses_global_options(): void
    {
        $enabled_payments = Payment::factory(2)->create(['enabled' => true]);
        $disabled_payments = Payment::factory(2)->create(['enabled' => false]);

        $delivery_method = Pickup::factory()->create(['payment_methods' => []]);

        $cart = Order::factory()->create(['payment_id' => 0]);
        $cart->updateCartLocation($delivery_method);

        // not set
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set and enabled globally
        $cart->setBillingInfo(['method' => $enabled_payments[0]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set and enabled globally
        $cart->setBillingInfo(['method' => $enabled_payments[1]->key]);
        $this->assertTrue($cart->hasSelectedBillingMethod());

        // set and disabled globally
        $cart->setBillingInfo(['method' => $disabled_payments[0]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());

        // set and disabled globally
        $cart->setBillingInfo(['method' => $disabled_payments[1]->key]);
        $this->assertFalse($cart->hasSelectedBillingMethod());
    }

    #[Test]
    public function it_can_get_its_consolidated_items_for_one_time_orders_without_fulfilled_quantity(): void
    {
        $order = Order::factory()->create();

        $product_one = Product::factory()->create();
        $product_two = Product::factory()->create();

        $bundle_product_one = Product::factory()->create();
        $bundle_product_one->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product_one->bundle()->attach($product_two->id, ['qty' => 3]);

        $item_one = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $bundle_product_one->id,
            'qty' => 2,
            'stock_status' => 'full',
            'fulfilled_qty' => 0
        ]);

        $item_two = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product_two->id,
            'qty' => 2,
            'stock_status' => 'full',
            'fulfilled_qty' => 0
        ]);

        $product_three = Product::factory()->create();

        $item_three = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product_three->id,
            'qty' => 1,
            'stock_status' => 'full',
            'fulfilled_qty' => 0
        ]);

        $consolidated_items = $order->consolidatedItems()->get();

        $this->assertCount(4, $consolidated_items);

        $this->assertTrue($consolidated_items->doesntContain(
            fn ($item) => $item->consolidated_product_id === $bundle_product_one->id)
        );

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_one) {
            return $item->consolidated_product_id === $product_one->id
                && $item->stock_status === 'full'
                && $item->consolidated_qty === 4
                && $item->consolidated_fulfilled_qty === 0;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_two) {
            return $item->consolidated_product_id === $product_two->id
                && $item->stock_status === 'full'
                && $item->consolidated_qty === 6
                && $item->consolidated_fulfilled_qty === 0;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_two) {
            return $item->consolidated_product_id === $product_two->id
                && $item->stock_status === 'full'
                && $item->consolidated_qty === 2
                && $item->consolidated_fulfilled_qty === 0;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_three) {
            return $item->consolidated_product_id === $product_three->id
                && $item->stock_status === 'full'
                && $item->consolidated_qty === 1
                && $item->consolidated_fulfilled_qty === 0;
        }));

    }

    #[Test]
    public function it_can_get_its_consolidated_items_for_subscription_orders_time_orders_with_fulfilled_quantities(): void
    {
        $order = Order::factory()->create();

        $product_one = Product::factory()->create();
        $product_two = Product::factory()->create();

        $bundle_product_one = Product::factory()->create();
        $bundle_product_one->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_product_one->bundle()->attach($product_two->id, ['qty' => 3]);

        $item_one = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $bundle_product_one->id,
            'qty' => 2,
            'stock_status' => 'short',
            'fulfilled_qty' => 1
        ]);

        $item_two = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product_two->id,
            'qty' => 2,
            'stock_status' => 'full',
            'fulfilled_qty' => 2
        ]);

        $product_three = Product::factory()->create();

        $item_three = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product_three->id,
            'qty' => 1,
            'stock_status' => 'out',
            'fulfilled_qty' => 0
        ]);

        $consolidated_items = $order->consolidatedItems()->get();

        $this->assertCount(4, $consolidated_items);

        $this->assertTrue($consolidated_items->doesntContain(
            fn ($item) => $item->consolidated_product_id === $bundle_product_one->id)
        );

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_one) {
            return $item->consolidated_product_id === $product_one->id
                && $item->stock_status === 'short'
                && $item->consolidated_qty === 4
                && $item->consolidated_fulfilled_qty === 2;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_two) {
            return $item->consolidated_product_id === $product_two->id
                && $item->stock_status === 'short'
                && $item->consolidated_qty === 6
                && $item->consolidated_fulfilled_qty === 3;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_two) {
            return $item->consolidated_product_id === $product_two->id
                && $item->stock_status === 'full'
                && $item->consolidated_qty === 2
                && $item->consolidated_fulfilled_qty === 2;
        }));

        $this->assertTrue($consolidated_items->contains(function ($item) use ($product_three) {
            return $item->consolidated_product_id === $product_three->id
                && $item->stock_status === 'out'
                && $item->consolidated_qty === 1
                && $item->consolidated_fulfilled_qty === 0;
        }));

    }

    #[Test]
    public function it_migrates_and_returns_its_default_address_when_user_does_not_have_an_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $attributes = Order::defaultShippingAttributes($user);

        $this->assertEquals($user->street, $attributes['shipping_street']);
        $this->assertEquals($user->street_2, $attributes['shipping_street_2']);
        $this->assertEquals($user->city, $attributes['shipping_city']);
        $this->assertEquals($user->state, $attributes['shipping_state']);
        $this->assertEquals($user->zip, $attributes['shipping_zip']);
        $this->assertEquals(app(SettingsService::class)->farmCountry(), $attributes['shipping_country']);
    }

    #[Test]
    public function it_can_resolve_its_default_address_when_user_has_a_non_default_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $address = Address::factory()->create([
            'street' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'CA',
        ]);

        $street_2 = $this->faker->streetAddress();

        $user->addresses()->attach($address->id, [
            'name' => 'something',
            'street_2' => $street_2,
            'is_default' => false
        ]);

        $this->assertEquals([
            'shipping_street' => $user->street,
            'shipping_street_2' => $user->street_2,
            'shipping_city' => $user->city,
            'shipping_state' => $user->state,
            'shipping_zip' => $user->zip,
            'shipping_country' => 'USA',
        ], Order::defaultShippingAttributes($user));
    }

    #[Test]
    public function it_can_resolve_its_default_address_when_user_has_a_default_address(): void
    {
        $user = User::factory()->create([
            'street' => $this->faker->streetAddress(),
            'street_2' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
        ]);

        $address = Address::factory()->create([
            'street' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'postal_code' => $this->faker->postcode(),
            'country' => 'CA',
        ]);

        $street_2 = $this->faker->streetAddress();

        $user->addresses()->attach($address->id, [
            'name' => 'something',
            'street_2' => $street_2,
            'is_default' => true
        ]);

        $this->assertEquals([
            'shipping_street' => $address->street,
            'shipping_street_2' => $street_2,
            'shipping_city' => $address->city,
            'shipping_state' => $address->state,
            'shipping_zip' => $address->postal_code,
            'shipping_country' => 'CA',
        ], Order::defaultShippingAttributes($user));
    }

    #[Test]
    public function it_can_get_its_user_shipping_info(): void
    {
        $user = User::factory()->create([
            'street' => '123 Main St',
            'street_2' => 'Apt 1',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '12345',
            'country' => 'USA',
        ]);

        $order = Order::factory()->create([
            'shipping_street' => '923 Main St',
            'shipping_street_2' => 'Apt 9',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '92345',
            'shipping_country' => 'USA',
        ]);

        $shipping_info = $order->getShippingInfo();

        $this->assertEquals('923 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 9', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('92345', $shipping_info['zip']);
        $this->assertEquals('USA', $shipping_info['country']);

        $address = Address::factory()->create([
            'street' => '223 Main St',
            'city' => 'Test',
            'state' => 'TE',
            'postal_code' => '22345',
            'country' => 'USA',
        ]);

        $user->addresses()->attach($address->id, ['name' => 'Non default', 'street_2' => 'Apt 2', 'is_default' => false]);

        $shipping_info = $order->getShippingInfo();

        $this->assertEquals('923 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 9', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('92345', $shipping_info['zip']);
        $this->assertEquals('USA', $shipping_info['country']);

        $user->addresses()->detach($address->id);

        $user->addresses()->attach($address->id, ['name' => 'Default', 'street_2' => 'Apt 3', 'is_default' => true]);

        $shipping_info = $order->getShippingInfo();

        $this->assertEquals('923 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 9', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('92345', $shipping_info['zip']);
        $this->assertEquals('USA', $shipping_info['country']);

        $order->setShippingInfo([
            'street' => '423 Main St',
            'street_2' => 'Apt 4',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '42345',
            'country' => 'USA',
        ]);

        $shipping_info = $order->getShippingInfo();

        $this->assertEquals('423 Main St', $shipping_info[ 'street']);
        $this->assertEquals('Apt 4', $shipping_info['street_2']);
        $this->assertEquals('Test', $shipping_info['city']);
        $this->assertEquals('TE', $shipping_info['state']);
        $this->assertEquals('42345', $shipping_info['zip']);
        $this->assertEquals('USA', $shipping_info['country']);
    }

    #[Test]
    public function it_resolves_order_has_unfulfilled_items_correctly(): void
    {
        $product = Product::factory()->create(['inventory' => 5]);
        $order = Order::factory()->create();

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'full',
            'qty' => 5,
            'fulfilled_qty' => 5,
        ]);

        $this->assertFalse($order->hasUnfulfilledItems());

        $product = Product::factory()->create(['inventory' => 0]);
        $order = Order::factory()->create();

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'out',
            'qty' => 5,
            'fulfilled_qty' => 0,
        ]);

        $this->assertTrue($order->hasUnfulfilledItems());

        $product = Product::factory()->create(['inventory' => 3]);
        $order = Order::factory()->create();

        OrderItem::factory()->create([
            'product_id' => $product->id,
            'order_id' => $order->id,
            'stock_status' => 'short',
            'qty' => 5,
            'fulfilled_qty' => 3,
        ]);

        $this->assertTrue($order->hasUnfulfilledItems());
    }

    #[Test]
    public function it_knows_if_its_a_gift(): void
    {
        $order = Order::factory()->make(['recipient_email' => null]);

        $this->assertFalse($order->isGift());

        $order->recipient_email = '<EMAIL>';
        $this->assertTrue($order->isGift());

    }

    #[Test]
    public function it_calculates_pack_deadline_correctly_when_schedule_pack_deadlines_are_set()
    {
        $order = Order::factory()->create([
            'pickup_date' => '2023-12-25',
            'pickup_id' => Pickup::factory()->create([
                'schedule_id' => Schedule::factory()->create([
                    'pack_deadline_days_before' => 2,
                    'pack_deadline_hours_before' => 5,
                ])->id,
            ])->id,
        ]);

        $this->assertEquals('2023-12-23 18:59:59', $order->pack_deadline_at->toDateTimeString());

        $order->pickup_date = '2023-12-28';
        $order->save();

        $this->assertEquals('2023-12-26 18:59:59', $order->pack_deadline_at->toDateTimeString());
    }

    #[Test]
    public function it_calculates_pack_deadline_correctly_when_schedule_pack_deadlines_are_not_set()
    {
        $order = Order::factory()->create([
            'pickup_date' => '2023-12-25',
            'pickup_id' => Pickup::factory()->create([
                'schedule_id' => Schedule::factory()->create([
                    'pack_deadline_days_before' => null,
                    'pack_deadline_hours_before' => null,
                ])->id,
            ])->id,
        ]);

        $this->assertEquals('2023-12-25 23:59:59', $order->pack_deadline_at->toDateTimeString());
    }

    #[Test]
    public function it_calculates_pack_deadline_correctly_when_pickup_has_no_schedule()
    {
        $order = Order::factory()->create([
            'pickup_date' => '2023-12-25',
            'pickup_id' => Pickup::factory()->create([
                'schedule_id' => null,
            ])->id,
        ]);

        $this->assertEquals('2023-12-25 23:59:59', $order->pack_deadline_at->toDateTimeString());
    }

    #[Test]
    public function it_calculates_pack_deadline_correctly_when_delivery_method_is_not_set()
    {
        $order = Order::factory()->create([
            'pickup_date' => '2023-12-25',
            'pickup_id' => 189127391,
        ]);

        $this->assertEquals('2023-12-25 23:59:59', $order->pack_deadline_at->toDateTimeString());
    }

    #[Test]
    public function it_determines_if_its_a_first_time_order(): void
    {
        $order = Order::factory()->make(['first_time_order' => 0]);

        $this->assertFalse($order->isFirstTimeOrder());

        $order->first_time_order = 1;

        $this->assertTrue($order->isFirstTimeOrder());
    }

    #[Test]
    public function it_knows_if_it_contains_packing_groups(): void
    {
        $group_1 = PackingGroup::factory()->create(['title' => 'Group 1']);
        $group_2 = PackingGroup::factory()->create(['title' => 'Group 2']);
        $group_3 = PackingGroup::factory()->create(['title' => 'Group 3']);

        $product_1 = Product::factory()->create(['inventory_type' => $group_1->id]);
        $product_2 = Product::factory()->create(['inventory_type' => $group_2->id]);

        $order = Order::factory()->create();
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product_1->id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product_2->id]);

        $this->assertTrue($order->containsPackingGroups([$group_1->id]));
        $this->assertTrue($order->containsPackingGroups([$group_2->id]));
        $this->assertFalse($order->containsPackingGroups([$group_3->id]));
        $this->assertTrue($order->containsPackingGroups([$group_1->id, $group_3->id]));
    }

    #[Test]
    public function it_get_its_own_customers_other_orders(): void
    {
        $order = Order::factory()->create();

        $other_orders = Order::factory(2)->create(['customer_id' => $order->customer_id]);

        $other_customer_orders = Order::factory(2)->create();

        $this->assertEquals(3, $order->customerOrders()->count());
    }

    #[Test]
    public function it_knows_if_it_is_paid(): void
    {
        $order = Order::factory()->make(['paid' => null]);

        $this->assertFalse($order->is_paid);

        $order->paid = false;
        $this->assertFalse($order->is_paid);

        $order->paid = true;
        $this->assertTrue($order->is_paid);
    }

    #[Test]
    public function it_knows_if_it_is_not_paid(): void
    {
        $order = Order::factory()->make(['paid' => null]);

        $this->assertTrue($order->is_not_paid);

        $order->paid = false;
        $this->assertTrue($order->is_not_paid);

        $order->paid = true;
        $this->assertFalse($order->is_not_paid);
    }
}
