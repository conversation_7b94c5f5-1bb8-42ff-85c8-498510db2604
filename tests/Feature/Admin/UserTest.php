<?php

namespace Tests\Feature\Admin;

use App\Events\User\UserWasRegistered;
use App\Mail\PasswordReminder;
use App\Mail\UserWelcome;
use App\Models\Address;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\User;
use App\Notifications\ForgotPassword;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\UserRole;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Routing\Middleware\ThrottleRequestsWithRedis;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UserTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_view_users(): void
    {
        $this->get(route('admin.users.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_view_admin_users(): void
    {
        $this->get(route('admin.users.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_users(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.users.index'))
            ->assertOk()
            ->assertViewIs('users.index')
            ->assertViewHasAll([
                'users',
                'savedFilters',
                'appliedFilter',
                'appliedFilters'
            ]);
    }

    #[Test]
    public function it_sorts_by_descending_created_at_by_default(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'role_id' => UserRole::admin(),
            'created_at' => now()->addDays(2),
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'created_at' => now()->addDays(3),
        ]);

        $this->actingAs($user_one)
            ->get(route('admin.users.index'))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) < $userIds->search($user_one->id);
            });
    }

    #[Test]
    public function it_can_sort_users_by_attributes(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'first_name' => 'ab',
            'last_name' => 'cd',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'first_name' => 'wz',
            'last_name' => 'yz',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['orderBy' => 'first_name', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) < $userIds->search($user_two->id);
            });

        $this->get(route('admin.users.index', ['orderBy' => 'first_name', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) < $userIds->search($user_one->id);
            });

        $this->get(route('admin.users.index', ['orderBy' => 'last_name', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) < $userIds->search($user_two->id);
            });

        $this->get(route('admin.users.index', ['orderBy' => 'last_name', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(fn(User $user) => $user->id);

                return $userIds->search($user_two->id) < $userIds->search($user_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_asc_when_using_invalid_sort_attribute(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'first_name' => 'ab',
            'last_name' => 'cd',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'first_name' => 'wz',
            'last_name' => 'yz',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['orderBy' => 'first_name', 'sort' => 'abc']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) < $userIds->search($user_two->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_name(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'first_name' => 'ab',
            'last_name' => 'cd',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'first_name' => 'wx',
            'last_name' => 'yz',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['users' => 'x y']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_name_with_apostrophe(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'first_name' => 'ab',
            'last_name' => 'cd',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'first_name' => 'wx\'s',
            'last_name' => 'yz',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['users' => 'x\'s y']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_email(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'email' => '<EMAIL>',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['users' => '<EMAIL>']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_phone(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'phone' => '************',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'phone' => '************',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['users' => '************']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_accounting_id(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'first_name' => 'accounting_one_first',
            'last_name' => 'accounting_one_last',
            'phone' => '************',
            'accounting_id' => 'other_accounting_id',
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'first_name' => 'accounting_two_first',
            'last_name' => 'accounting_two_last',
            'phone' => '************',
            'accounting_id' => 'special_account_id',
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['users' => 'special_account_id']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_role(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'role_id' => UserRole::customer(),
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['role_id' => UserRole::admin()]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) === false && $userIds->search($user_two->id) !== false;
            });

        $this->get(route('admin.users.index', ['role_id' => [UserRole::admin()]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) === false && $userIds->search($user_two->id) !== false;
            });

        $this->get(route('admin.users.index', ['role_id' => UserRole::customer()]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) === false && $userIds->search($user_one->id) !== false;
            });

        $this->get(route('admin.users.index', ['role_id' => [UserRole::customer()]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) === false && $userIds->search($user_one->id) !== false;
            });
    }

    #[Test]
    public function it_can_filter_user_by_types(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'role_id' => UserRole::customer(),
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['types' => UserRole::admin()]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['types' => [UserRole::admin()]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['types' => UserRole::customer()]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->contains(fn (User $user) => $user->id === $user_one->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_two->id);
            });

        $this->get(route('admin.users.index', ['types' => [UserRole::customer()]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->contains(fn (User $user) => $user->id === $user_one->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_two->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_schedule(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create([
            'role_id' => UserRole::customer(),
        ]);

        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'pickup_point' => $pickup->id
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['schedule' => $schedule->id]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['schedule' => [$schedule->id]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_pickup(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create();

        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'pickup_point' => $pickup->id
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['pickup_id' => $pickup->id]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['pickup_id' => [$pickup->id]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_users_by_last_purchase_date(): void
    {
        Carbon::setTestNow(now());

        /** @var User $user_one  */
        $user_one = User::factory()->create();

        $order_one = Order::factory()->create([
            'customer_id' => $user_one->id,
            'confirmed' => true,
            'canceled' => false,
            'pickup_date' => today()->subDay()
        ]);

        /** @var User $user_two */
        $user_two = User::factory()->create(['role_id' => UserRole::admin()]);

        $order_two = Order::factory()->create([
            'customer_id' => $user_two->id,
            'confirmed' => true,
            'canceled' => false,
            'pickup_date' => today()->addDays(3)
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.users.index', ['last_purchase' => [
                'start' => today()->addDays(2)->format('Y-m-d'),
                'end' => today()->addDays(5)->format('Y-m-d')
            ]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['last_purchase' => [
            'start' => today()->subDays(5)->format('Y-m-d'),
            'end' => today()->format('Y-m-d')
        ]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->contains(fn (User $user) => $user->id === $user_one->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_two->id);
            });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_user_by_state(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create(['state' => 'AB']);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'state' => 'XY'
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['state' => 'XY']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_referral(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create(['referral_user_id' => 0]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'referral_user_id' => User::factory()
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['referral_user_id' => 'XY']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_newsletter(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create(['newsletter' => 0]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'newsletter' => 1
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['newsletter' => 1]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) === false && $userIds->search($user_two->id) !== false;
            });

        $this->get(route('admin.users.index', ['newsletter' => 0]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) === false && $userIds->search($user_one->id) !== false;
            });
    }

    #[Test]
    public function it_can_filter_user_by_active(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create(['active' => 0]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'active' => 1
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['active' => 1]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) === false && $userIds->search($user_two->id) !== false;
            });

        $this->get(route('admin.users.index', ['active' => 0]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_two->id) === false && $userIds->search($user_one->id) !== false;
            });
    }

    #[Test]
    public function it_can_filter_user_by_order_deadline_email_reminder(): void
    {
        /** @var User $user_one  */
        $user_one = User::factory()->create(['order_deadline_email_reminder' => 0]);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin(),
            'order_deadline_email_reminder' => 1
        ]);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['order_deadline_email_reminder' => 1]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(function (User $user) {
                    return $user->id;
                });

                return $userIds->search($user_one->id) === false && $userIds->search($user_two->id) !== false;
            });

        $this->get(route('admin.users.index', ['order_deadline_email_reminder' => 0]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                $userIds = $arg->map(fn(User $user) => $user->id);

                return $userIds->search($user_two->id) === false
                    && $userIds->search($user_one->id) !== false;
            });
    }

    #[Test]
    public function it_can_filter_user_by_tags(): void
    {
        $tag_one = Tag::factory()->create();
        $tag_two = Tag::factory()->create();

        /** @var User $user_one  */
        $user_one = User::factory()->create();
        $user_one->tags()->attach($tag_one);

        /** @var User $user_two */
        $user_two = User::factory()->create([
            'role_id' => UserRole::admin()
        ]);
        $user_two->tags()->attach($tag_one);
        $user_two->tags()->attach($tag_two);

        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['tags' => $tag_one->id]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->contains(fn (User $user) => $user->id === $user_one->id)
                    && $arg->contains(fn (User $user) => $user->id === $user_two->id);
            });

        $this->get(route('admin.users.index', ['tags' => [$tag_two->id]]))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->doesntContain(fn (User $user) => $user->id === $user_one->id)
                    && $arg->contains(fn (User $user) => $user->id === $user_two->id);
            });
    }

    #[Test]
    public function it_can_filter_user_by_subscription_status(): void
    {
        $this->withoutMiddleware(ThrottleRequestsWithRedis::class);

        /** @var User $user_one  */
        $user_one = User::factory()->create();
        RecurringOrder::factory()->create(['customer_id' => $user_one->id, 'deleted_at' => now()]);
        /** @var User $user_two */
        $user_two = User::factory()->create(['role_id' => UserRole::admin()]);
        RecurringOrder::factory()->create(['customer_id' => $user_two->id]);


        $this->actingAs($user_two)
            ->get(route('admin.users.index', ['subscription_status' => 'active']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_two, $user_one) {
                return $arg->contains(fn (User $user) => $user->id === $user_two->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_one->id);
            });

        $this->get(route('admin.users.index', ['subscription_status' => 'inactive']))
            ->assertOk()
            ->assertViewHas('users', function (LengthAwarePaginator $arg) use ($user_one, $user_two) {
                return $arg->contains(fn (User $user) => $user->id === $user_one->id)
                    && $arg->doesntContain(fn (User $user) => $user->id === $user_two->id);
            });
    }

    public function test_visitor_can_register_for_customer_account(): void
    {
        $this->withoutMiddleware(ThrottleRequestsWithRedis::class);

        Mail::fake();
        Http::fake();

        Setting::updateOrCreate(['key' => 'send_customer_welcome_email'], ['value' => true]);

        $referralCode = (new User())->createReferralCode();

        $this->get('/register?referral_code=' . $referralCode)
            ->assertViewIs('theme::authentication.register')
            ->assertCookie('referral_code', $referralCode, false);

        $pickup = Pickup::factory()->create();

        $email = 'test-' . \Str::random() . '@grazecart.com';

        $this->post(route('register'), [
            'first_name' => 'Levi',
            'last_name' => 'Smith',
            'email' => $email,
            'password' => '********',
            'pickup_point' => $pickup->id,
            'subscribe_to_newsletter' => 'on'
        ])
            ->assertRedirect()
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas('users', [
            'email' => $email,
            'first_name' => 'Levi',
            'last_name' => 'Smith',
            'pickup_point' => $pickup->id,
            'newsletter' => true
        ]);

        $user = User::where('email', $email)->first();

        Mail::assertQueued(
            UserWelcome::class,
            function (UserWelcome $mailable) use ($user) {
                return $mailable->hasTo($user->email);
            }
        );

        $user = User::where('email', $email)->first();

        $this->assertAuthenticatedAs($user);
    }

    public function test_referral_credit_is_applied_when_creating_an_account(): void
    {
        $this->withoutMiddleware(ThrottleRequestsWithRedis::class);

        Http::fake();
        Queue::fake();
        $referral_payout_global = 300;
        $referral_payout_user = 500;
        $referral_payout_referrer = 600;

        Setting::updateOrCreate(['key' => 'enable_referrals'], ['value' => true]);
        Setting::updateOrCreate(['key' => 'referral_payout'], ['value' => formatCurrencyForDB($referral_payout_global)]);
        Setting::updateOrCreate(['key' => 'referral_bonus'], ['value' => formatCurrencyForDB($referral_payout_referrer)]);

        $referralCode = (new User())->createReferralCode();

        $referrer = User::factory()->create([
            'referral_code' => $referralCode,
            'referral_payout' => formatCurrencyForDB($referral_payout_user)
        ]);

        $this->get('/register?referral_code=' . $referralCode)
            ->assertViewIs('theme::authentication.register')
            ->assertPlainCookie('referral_code', $referralCode);

        $pickup = Pickup::factory()->create();

        $email = Str::random(5) . '@grazecart.com';

        $this->withUnencryptedCookie('referral_code', $referralCode)
            ->post(route('register'), [
            'first_name' => 'Levi',
            'last_name' => 'Smith',
            'email' => $email,
            'password' => '********',
            'pickup_point' => $pickup->id,
            'subscribe_to_newsletter' => 'on',
        ])
            ->assertRedirect()
            ->assertSessionHasNoErrors();

        $newCustomer = User::where('email', $email)->first();

        $this->assertDatabaseHas('users', [
            'email' => $email,
            'first_name' => 'Levi',
            'last_name' => 'Smith',
            'pickup_point' => $pickup->id,
            'newsletter' => true,
            'referral_user_id' => $referrer->id,
            'credit' => formatCurrencyForDB($referral_payout_user + $referral_payout_global)
        ]);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $newCustomer->id,
            'description' => 'For being referred by ' . $referrer->full_name,
            'event_id' => 'credit_applied',
            'user_id' => $newCustomer->id,
            'metadata' => json_encode([
                'amount' => formatCurrencyForDB($referral_payout_user + $referral_payout_global),
            ])
        ]);
    }

    public function test_customer_can_log_in(): void
    {
        $user = User::factory()->create([
            'password' => '12335'
        ]);

        $this->get('/login')->assertViewIs('theme::authentication.login');

        $this->post('/login', [
            'email' => $user->email,
            'password' => '12335'
        ]);

        $this->assertAuthenticatedAs($user);
    }

    #[Test]
    public function it_throttles_customer_log_in_attempts(): void
    {
        $user = User::factory()->create();

        $this->get('/login')->assertViewIs('theme::authentication.login');

        $credentials = [
            'email' => $user->email,
            'password' => '12335'
        ];

        $flashNotificationMessage = [
            'message' => 'The credentials you entered were incorrect.',
            'level' => 'info'
        ];

        $this->post('/login', $credentials)
            ->assertRedirect(route('login'))
            ->assertSessionHas('flash_notification', $flashNotificationMessage);

        $this->post('/login', $credentials)
            ->assertRedirect(route('login'))
            ->assertSessionHas('flash_notification', $flashNotificationMessage);

        $this->post('/login', $credentials)
            ->assertRedirect(route('login'))
            ->assertSessionHas('flash_notification', $flashNotificationMessage);

        $this->post('/login', $credentials)
            ->assertRedirect(route('login'))
            ->assertSessionHas('flash_notification', $flashNotificationMessage);

        $this->post('/login', $credentials)
            ->assertRedirect(route('login'))
            ->assertSessionHas('flash_notification', $flashNotificationMessage);

        $this->post('/login', $credentials)
            ->assertStatus(429);
    }

    public function test_customer_can_logout(): void
    {
        $user = User::factory()->create([
            'password' => '12335',
            'pickup_point' => function () {
                return Pickup::factory()->create()->id;
            }
        ]);

        $this->actingAs($user);

        $this->get('/logout');

        $this->assertGuest();
    }

    public function test_user_is_redirected_at_login_when_redirect_url_param_is_provided(): void
    {
        $user = User::factory()->create([
            'password' => '12335'
        ]);

        $redirectURL = '/account/orders';

        $this->get('/account/orders', [
            'redirect_to' => $redirectURL
        ])->assertCookie('last_viewed_page', url($redirectURL), false);

        $this->get('/login')
            ->assertViewIs('theme::authentication.login')
            ->assertCookie('last_viewed_page', url($redirectURL), false);

        $this->call('POST', '/login', [
            'email' => $user->email,
            'password' => '12335'
        ], [
            'last_viewed_page' => url($redirectURL, [], true)
        ])
            ->assertRedirect(url($redirectURL, [], true));

        $this->assertAuthenticatedAs($user);
    }

    public function test_customer_is_redirected_after_registration_when_redirect_url_param_is_provided(): void
    {
        Carbon::setTestNow();

        $this->withoutMiddleware(ThrottleRequestsWithRedis::class);

        $this->disableCookieEncryption();

        Queue::fake();
        Http::fake();

        $pickup = Pickup::factory()->create();

        $redirectURL = '/welcome';

        $this->get('/register?redirect_to=welcome')
            ->assertCookie('last_viewed_page', url($redirectURL), false);

        $this->withCookie('last_viewed_page', url($redirectURL, [], true))
            ->post(route('register'), [
                'first_name' => 'Levi',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'password' => '********',
                'pickup_point' => $pickup->id,
                'subscribe_to_newsletter' => 'on'
            ])
            ->assertRedirect(url($redirectURL . '?firstlogin=' . now()->timestamp, [], true))
            ->assertSessionHasNoErrors();

        $this->assertAuthenticatedAs(User::where('email', '<EMAIL>')->first());

        Carbon::setTestNow();
    }

    public function test_lead_is_redirected_after_registering_as_lead_when_redirect_url_param_is_provided(): void
    {
        $redirectURL = url('/welcome', []);

        $this->get('/register?redirect_to=welcome')
            ->assertCookie('last_viewed_page', $redirectURL, false);

        $pickup = Pickup::factory()->create();

        $this->withUnencryptedCookie('last_viewed_page', $redirectURL)
            ->post(route('register.lead.store'), [
                'first_name' => 'Test first',
                'last_name' => 'Test last',
                'email' => '<EMAIL>',
                'zip' => 46783,
                'pickup_point' => $pickup->id,
                'address' => [
                    'lat' => 123.45,
                    'lng' => 67.89,
                ]
            ], [
                'last_viewed_page' => $redirectURL
            ])
            ->assertRedirect($redirectURL);

        $this->assertDatabaseHas('leads', [
            'first_name' => 'Test first',
            'last_name' => 'Test last',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_owner_can_login_to_dashboard(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********',
            'role_id' => UserRole::owner(),
        ]);

        $this->actingAs($user)->get('/admin')->assertViewIs('dashboard.index');
    }

    public function test_admin_can_login_to_dashboard(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********',
            'role_id' => UserRole::admin(),
        ]);

        $this->actingAs($user)->get('/admin')->assertViewIs('dashboard.index');
    }

    public function test_support_user_can_login_to_dashboard(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********',
            'role_id' => UserRole::support(),
        ]);

        $this->actingAs($user)->get('/admin')->assertViewIs('dashboard.index');
    }

    public function test_customer_can_not_login_to_dashboard(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********',
            'role_id' => UserRole::customer(),
        ]);

        $this->actingAs($user)->get('/admin')->assertRedirect('/admin/login');
    }

    public function test_editor_can_login_to_admin_and_gets_redirected_to_posts(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********',
            'role_id' => UserRole::editor(),
        ]);

        $this->actingAs($user)->get('/admin')->assertRedirect('/admin/posts');
    }

    public function test_customer_can_request_password_reset(): void
    {
        Mail::fake();
        Http::fake();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => '********'
        ]);

        $response = $this->post('/password/forgot', [
            'email' => $user->email,
        ]);

        $originalPassword = $user->password;

        $this->assertDatabaseHas('password_resets', [
            'email' => $user->email
        ]);

        $token = DB::table('password_resets')->where('email', $user->email)->pluck('token')->first();
        $response->assertRedirect('/password/forgot');

        Mail::assertQueued(PasswordReminder::class, function ($mail) use ($user, $token) {
            return $mail->hasTo($user->email);
        });

        $this->get('/password/reset/' . $token)
            ->assertViewIs('theme::authentication.reset-password');

        $response = $this->post('/password/reset', [
            'token' => $token,
            'email' => $user->email,
            'password' => '54321',
            'password_confirmation' => '54321'
        ]);

        $response->assertRedirect('/login');
        $this->assertFalse(User::where('email', $user->email)->first()->password == $originalPassword);
        $this->assertDatabaseMissing('password_resets', [
            'email' => $user->email
        ]);
    }

    public function test_admin_can_request_password_reset(): void
    {
        Notification::fake();
        Http::fake();

        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'role_id' => UserRole::admin(),
            'password' => '********'
        ]);

        $originalPassword = $user->password;

        $response = $this->post('/admin/password/forgot', [
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseHas('password_resets', [
            'email' => $user->email
        ]);

        $token = DB::table( 'password_resets')
            ->where('email', $user->email)->pluck('token')
            ->first();

        $response->assertRedirect('/admin/password/forgot');

        Notification::assertSentTo($user, ForgotPassword::class);

        $this->get('/admin/password/reset/' . $token)
            ->assertViewIs('passwords.reset');

        $response = $this->post('/admin/password/reset', [
            'token' => $token,
            'email' => $user->email,
            'password' => '54321',
            'password_confirmation' => '54321'
        ]);

        $response->assertRedirect('/admin/login');
        $this->assertFalse(User::where('email', $user->email)->first()->password == $originalPassword);
    }

    #[Test]
    public function it_returns_null_when_user_has_no_orders(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_returns_null_when_user_order_is_in_canceled_status(): void
    {
        Carbon::setTestNow($now = now());

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::canceled(),
            'canceled' => false,
            'confirmed' => false,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_returns_null_when_user_order_is_canceled(): void
    {
        Carbon::setTestNow($now = now());

        /** @var User $user */
        $user = User::factory()->create();

        Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::processing(),
            'canceled' => true,
            'confirmed' => false,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_returns_null_when_user_order_is_completed(): void
    {
        Carbon::setTestNow($now = now());

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::completed(),
            'canceled' => false,
            'confirmed' => false,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_returns_null_when_user_order_is_picked_up(): void
    {
        Carbon::setTestNow($now = now());

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::pickedUp(),
            'canceled' => false,
            'confirmed' => false,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_returns_order_when_order_is_confirmed_and_ordering_mode_not_configured(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::confirmed(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_returns_order_when_order_is_confirmed_and_ordering_mode_is_configured_off(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::processing(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_returns_order_when_order_is_confirmed_and_there_is_no_pickup_date(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::processing(),
            'canceled' => false,
            'confirmed' => true,
            'pickup_date' => null,
            'blueprint_id' => RecurringOrder::factory(),
        ]);

        $this->assertEquals($order->id, $user->queryOpenOrder()?->id);
    }

    #[Test]
    public function it_returns_null_when_order_is_confirmed_and_ordering_mode_is_configured_on_and_the_deadline_date_is_in_the_past(): void
    {
        Carbon::setTestNow($now = now());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::processing(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->subDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_deadline_date_is_null(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => null,
            'pickup_date' => today()->addDays(3)
        ]);

        $this->assertNull($user->queryOpenOrder());
    }

    #[Test]
    public function it_does_not_fetch_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_pickup_date_has_passed(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true, 'pickup_date' => today()->subDay(), 'deadline_date' => null]);

        $this->assertNull($user->queryOpenOrder());

        Carbon::setTestNow();
    }

    #[Test]
    public function it_fetches_open_order_when_order_is_confirmed_and_ordering_mode_edits_are_enabled_and_pickup_date_has_not_passed(): void
    {
        Carbon::setTestNow(now());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'confirmed' => true,
            'pickup_date' => today(),
            'blueprint_id' => RecurringOrder::factory()
        ]);

        $open_order = $user->queryOpenOrder();
        $this->assertNotNull($open_order);
        $this->assertEquals($order->id, $open_order->id);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_fetches_recurring_open_order_when_order_instead_of_a_one_off_order(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();

        $recurring_order = Order::factory()->create([
            'blueprint_id' => RecurringOrder::factory(),
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDays(3),
            'pickup_date' => today()->addDays(3),
        ]);

        Order::factory()->create([
            'is_recurring' => false,
            'blueprint_id' => null,
            'customer_id' => $user->id,
            'confirmed' => true,
            'deadline_date' => today()->addDays(3),
            'pickup_date' => today()->addDays(3),
        ]);

        $open_order = $user->queryOpenOrder();

        $this->assertNotNull($open_order);
        $this->assertEquals($recurring_order->id, $open_order->id);
    }

    #[Test]
    public function it_returns_order_when_order_is_new(): void
    {
        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => false]);

        /** @var User $user */
        $user = User::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::confirmed(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3)
        ]);

        $open_order = $user->queryOpenOrder();
        $this->assertNotNull($open_order);
        $this->assertInstanceOf(Order::class, $open_order);
        $this->assertEquals($order->id, $open_order->id);
    }

    #[Test]
    public function it_returns_order_when_order_is_processing(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::processing(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'blueprint_id' =>  RecurringOrder::factory(),
        ]);

        $open_order = $user->queryOpenOrder();
        $this->assertNotNull($open_order);
        $this->assertInstanceOf(Order::class, $open_order);
        $this->assertEquals($order->id, $open_order->id);
    }

    #[Test]
    public function it_returns_order_when_order_is_packed(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::packed(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
            'blueprint_id' =>  RecurringOrder::factory(),
        ]);

        $open_order = $user->queryOpenOrder();
        $this->assertNotNull($open_order);
        $this->assertInstanceOf(Order::class, $open_order);
        $this->assertEquals($order->id, $open_order->id);
    }

    #[Test]
    public function it_returns_order_when_order_is_on_hold(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'status_id' => OrderStatus::onHold(),
            'canceled' => false,
            'confirmed' => true,
            'deadline_date' => today()->addDays(2),
            'pickup_date' => today()->addDays(3),
        ]);

        $open_order = $user->queryOpenOrder();
        $this->assertNotNull($open_order);
        $this->assertInstanceOf(Order::class, $open_order);
        $this->assertEquals($order->id, $open_order->id);
    }

    #[Test]
    public function test_email_alt_is_not_required_to_update_user_profile(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_alt' => ' ',
        ]);

        $this->put(route("admin.users.update", [$user]))
            ->assertSessionHasNoErrors();
    }

    #[Test]
    public function a_guest_cannot_create_a_user(): void
    {
        $this->post(route('admin.users.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_a_user(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.users.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_create_user_request(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.users.store'))
            ->assertInvalid([
                'first_name' => 'The first name field is required.',
                'last_name' => 'The last name field is required.',
                'email' => 'The email field is required.',
                'password' => 'The password field is required.',
            ]);

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'email' => 'abc@',
            ])
            ->assertInvalid([
                'email' => 'The email field must be a valid email address.',
            ]);

        $user = User::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'email' => $user->email,
            ])
            ->assertInvalid([
                'email' => 'The email has already been taken.',
            ]);

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'street' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'type' => '',
                'pickup_point' => ''
            ])
            ->assertInvalid([
                'street' => 'The street field is required.',
                'city' => 'The city field is required.',
                'state' => 'The state field is required.',
                'zip' => 'The zip field is required.',
                'type' => 'A user type is required.',
                'pickup_point' => 'You must provide a default pickup location.'
            ]);
    }

    #[Test]
    public function an_admin_can_create_a_user(): void
    {
        Event::fake([UserWasRegistered::class]);
        Http::fake();

        $location = Pickup::factory()->create();

        $response = $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'first_name' => 'first name',
                'last_name' => 'last name',
                'email' => '<EMAIL>',
                'password' => 'password',
                'pickup_point' => $location->id
            ])
            ->assertSessionDoesntHaveErrors();

        $expected_attributes = [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => '<EMAIL>',
            'pickup_point' => $location->id
        ];

        $this->assertDatabaseHas(User::class, $expected_attributes);

        $user = User::where($expected_attributes)->first();

        Event::assertDispatched(
            UserWasRegistered::class,
            fn(UserWasRegistered $event) => $event->user->id === $user->id
        );

        $response->assertRedirect(route('admin.users.edit', $user));
    }

    #[Test]
    public function it_throttles_user_creation(): void
    {
        Event::fake([UserWasRegistered::class]);
        Http::fake();

        $location = Pickup::factory()->create();

        $user_attributes = [
            [
                'first_name' => 'first name',
                'last_name' => 'last name',
                'email' => '<EMAIL>',
                'pickup_point' => $location->id
            ],
            [
                'first_name' => 'first name',
                'last_name' => 'last name',
                'email' => '<EMAIL>',
                'pickup_point' => $location->id
            ],
            [
                'first_name' => 'first name',
                'last_name' => 'last name',
                'email' => '<EMAIL>',
                'pickup_point' => $location->id
            ]
        ];

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), array_merge(['password' => 'password'], $user_attributes[0]))
            ->assertRedirect()
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(User::class, $user_attributes[0]);

        $this->post(route('admin.users.store'), array_merge(['password' => 'password'], $user_attributes[1]))
            ->assertRedirect()
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(User::class, $user_attributes[1]);

        $this->post(route('admin.users.store'), array_merge(['password' => 'password'], $user_attributes[2]))
            ->assertStatus(429);

        $this->assertDatabaseMissing(User::class, $user_attributes[2]);

        // another user does not hit limit
        $this->actingAsAdmin()
            ->post(route('admin.users.store'), array_merge(['password' => 'password'], $user_attributes[2]))
            ->assertRedirect()
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas(User::class, $user_attributes[2]);
    }

    #[Test]
    public function it_does_not_allow_emails_with_spaces_in_them(): void
    {
        Event::fake([UserWasRegistered::class]);

        $location = Pickup::factory()->create();

        $user_attributes = [
            'first_name' => 'first name',
            'last_name' => 'last name',
            'email' => 'test1 @email.com',
            'pickup_point' => $location->id
        ];

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), array_merge(['password' => 'password'], $user_attributes))
            ->assertRedirect()
            ->assertSessionHasErrors(['email' => 'The email field must be a valid email address.']);

        $this->assertDatabaseMissing(User::class, $user_attributes);
    }

    #[Test]
    public function it_renders_the_user_page_with_address_details_if_available(): void
    {
        $user = User::factory()->create([
            'street' => 'New Mountains User',
            'country' => 'USA',
        ]);

        $address = Address::factory()->create([
            'street' => 'New Mountains Address',
            'city' => 'West World',
            'state' => 'WW',
            'postal_code' => "1234",
            'country' => 'USA',
        ]);

        $user->addresses()->save($address, [
            'name' => 'New Mountains, West World, WW 1234',
            'street_2' => 'Dusty Roads',
            'is_default' => true
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.users.edit', compact('user')))
            ->assertOk()
            ->assertViewIs('users.edit')
            ->assertViewHas('user', function (User $arg) use ($user) {
                return $arg->id ===  $user->id
                    && $arg->addresses->first()->street === 'New Mountains Address';
            });
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
