<?php

namespace Tests\Feature\API;

use App\Actions\Cart\ConfirmDatabaseCartWithCart;
use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\CustomerSubscribed;
use App\Events\Subscription\RecurringOrderCreated;
use App\Models\Card;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DatabaseCartConfirmationTest extends TenantTestCase
{
    #[Test]
    public function it_requires_a_logged_in_user(): void
    {

        $this->postJson(route('api.carts.confirm'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_validates_the_cart_confirmation_request(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'))
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The type field is required.'],
                'id' => ['The id field is required.'],
                'purchase_type' => ['The purchase type field is required.'],
                'delivery_method_id' => ['The delivery method id field is required.'],
                'items' => ['The items field is required.'],
                'customer' => ['The customer field is required.'],
                'billing' => ['The billing field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'type' => 'foo',
                'purchase_type' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The selected type is invalid.'],
                'purchase_type' => ['The selected purchase type is invalid.'],
            ]);

        $db_cart = \App\Models\Cart::factory()->create();

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'type' => 'database',
                'id' => $db_cart->id,
            ])
            ->assertUnprocessable() // doesnt belong to auth user
            ->assertJsonFragment(['id' => ['The selected id is invalid.']]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['date_id' => $date->id])
            ->assertUnprocessable() // inactive date
            ->assertJsonFragment(['date_id' => ['The selected date id is invalid.']]);

        $location = Pickup::factory()->create(['status_id' => 2]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['delivery_method_id' => $location->id,])
            ->assertUnprocessable() // closed location
            ->assertJsonFragment(['delivery_method_id' => ['The selected delivery method id is invalid.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'items' => 'foo',
                'subscription' => 'foo',
                'customer' => 'foo',
                'shipping' => 'foo',
                'billing' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items' => ['The items field must be an array.'],
                'subscription' => ['The subscription field must be an array.'],
                'customer' => ['The customer field must be an array.'],
                'shipping' => ['The shipping field must be an array.'],
                'billing' => ['The billing field must be an array.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => []])
            ->assertUnprocessable()
            ->assertJsonFragment(['items' => ['The items field is required.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => [[]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The items.0.product_id field is required.'],
                'items.0.quantity' => ['The items.0.quantity field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['items' => [[
                'product_id' => 'foo',
                'quantity' => 0,
            ]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The selected items.0.product_id is invalid.'],
                'items.0.quantity' => ['The items.0.quantity field must be at least 1.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['purchase_type' => 'recurring'])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'subscription' => ['The subscription field is required when purchase type is recurring.'],
                'subscription.frequency' => ['The subscription.frequency field is required when purchase type is recurring.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), [
                'purchase_type' => 'recurring',
                'subscription' => ['product_incentive_id' => 'foo']
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'subscription.product_incentive_id' => ['The selected subscription.product incentive id is invalid.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['customer' => [
                'save_for_later' => 'foo',
                'opt_in_to_sms' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'customer.first_name' => ['The customer.first name field is required.'],
                'customer.last_name' => ['The customer.last name field is required.'],
                'customer.email' => ['The customer.email field is required.'],
                'customer.phone' => ['The customer.phone field is required.'],
                'customer.save_for_later' => ['The customer.save for later field must be true or false.'],
                'customer.opt_in_to_sms' => ['The customer.opt in to sms field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
                'save_for_later' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.method' => ['The selected billing.method is invalid.'],
                'billing.save_for_later' => ['The billing.save for later field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => true, 'key' => 'other']);
        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
                'source_id' => null
            ]])
            ->assertUnprocessable()
            ->assertJsonMissingValidationErrors([
                'billing.source_id'
            ]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), ['billing' => [
                'method' => $method->key,
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.source_id' => ['The billing.source id field is required when billing.method is card.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'),[
                'notes' => Str::random(1001),
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'notes' => ['The notes field must not be greater than 1000 characters.'],
            ]);
    }

    #[Test]
    public function it_confirms_a_one_time_order_delivery_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $order = Order::factory()->create();

        $this->mock(ConfirmDatabaseCartWithCart::class, function (MockInterface $mock) use ($order, $db_cart, $cart) {
            $mock->shouldReceive('handle')
                ->once()
                ->andReturn($order);
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        $order = $user->orders()->latest()->first();

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => 'standard',
            'unit_price' => 1000,
            'original_unit_price' => 1000,
            'qty' => 2,
            'original_qty' => 2,
            'weight' => $product->weight * 2,
            'original_weight' => $product->weight * 2,
            'store_price' => 1000,
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'created_year' => today()->year,
            'created_month' =>  today()->month,
            'created_day' =>  today()->day,
        ]);

        $this->assertDatabaseMissing(\App\Models\Cart::class, [
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_subscription_order_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class, CustomerSubscribed::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14]]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true]);
        $next_date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true, 'pickup_date' => today()->addDays(28)]);

        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $schedule->id]);

        /** @var \App\Models\Cart $db_cart */
        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();


        $product = Product::factory()->create(['unit_price' => '10.00']);
        $db_cart->addProduct($product, 2);

        $promo = Product::factory()->create();
        $db_cart->setCartAsSubscriptionPurchase(14, $promo->id);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'recurring',
            'subscription' => [
                'frequency' => 14,
                'product_incentive_id' => $promo->id,
            ],
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($promo) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn($promo->id);
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(3);
        });


        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        $order = $user->orders()->latest()->first();

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'user_id' => $order->customer_id,
            'title' => $product->title,
            'type' => 'standard',
            'unit_price' => 1000,
            'original_unit_price' => 1000,
            'qty' => 2,
            'original_qty' => 2,
            'weight' => $product->weight * 2,
            'original_weight' => $product->weight * 2,
            'store_price' => 1000,
            'unit_of_issue' => $product->unit_of_issue,
            'taxable' => $product->taxable,
            'created_year' => today()->year,
            'created_month' =>  today()->month,
            'created_day' =>  today()->day,
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $promo->id,
            'user_id' => $order->customer_id,
            'title' => $promo->title,
            'type' => 'promo',
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'customer_id' => $user->id,
            'reorder_frequency' => 14,
            'fulfillment_id' => $location->id,
            'generate_at' => $next_date->order_end_date->subDays(3)->setTime(23,59,59),
            'ready_at' => $next_date->pickup_date->setTime(0,0,0)
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'type' => 'promo',
            'product_id' => $promo->id
        ]);

        $this->assertDatabaseMissing(\App\Models\Cart::class, [
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertDispatched(RecurringOrderCreated::class);
        Event::assertDispatched(CustomerSubscribed::class);
    }

    #[Test]
    public function it_confirms_a_subscription_order_with_a_cart_without_a_promo_item(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class, CustomerSubscribed::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14]]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true]);
        $next_date = Date::factory()->create(['schedule_id' => $schedule->id, 'active' => true, 'pickup_date' => today()->addDays(28)]);

        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $schedule->id]);

        /** @var \App\Models\Cart $db_cart */
        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();


        $product = Product::factory()->create(['unit_price' => '10.00']);
        $db_cart->addProduct($product, 2);

        $db_cart->setCartAsSubscriptionPurchase(14, null);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'recurring',
            'subscription' => [
                'frequency' => 14,
                'product_incentive_id' => null,
            ],
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('hasProductIncentive')->andReturnFalse();
            $mock->shouldReceive('defaultProductIncentiveId')->andReturnNull();
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(3);
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => '123 Test St',
            'shipping_street_2' => 'Apt Test',
            'shipping_city' => 'Test',
            'shipping_state' => 'TE',
            'shipping_zip' => '12345',
            'shipping_country' => 'US',
            'payment_id' => $method->id
        ]);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'customer_id' => $user->id,
            'reorder_frequency' => 14,
            'fulfillment_id' => $location->id,
            'generate_at' => $next_date->order_end_date->subDays(3)->setTime(23,59,59),
            'ready_at' => $next_date->pickup_date->setTime(0,0,0)
        ]);

        $subscription = RecurringOrder::where([
            'customer_id' => $user->id,
            'reorder_frequency' => 14,
            'fulfillment_id' => $location->id
        ])->first();

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'type' => 'promo',
        ]);

        $this->assertDatabaseMissing(\App\Models\Cart::class, [
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertDispatched(RecurringOrderCreated::class);
        Event::assertDispatched(CustomerSubscribed::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_pickup_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => null,
            'shipping_street_2' => '',
            'shipping_city' => null,
            'shipping_state' => null,
            'shipping_zip' => null,
            'shipping_country' => null,
            'payment_id' => $method->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_pickup_without_a_date_with_a_cart(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => null]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => '',
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'customer_first_name' => 'First test',
            'customer_last_name' => 'Last test',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'shipping_street' => null,
            'shipping_street_2' => '',
            'shipping_city' => null,
            'shipping_state' => null,
            'shipping_zip' => null,
            'shipping_country' => null,
            'payment_id' => $method->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_confirms_a_one_time_order_with_a_coupon(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $coupon = Coupon::factory()->create(['discount_type' => 'fixed', 'discount_amount' => 100]);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        /** @var \App\Models\Cart $db_cart */
        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => '',
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'discounts' => [
                'coupons' => [
                    ['name' => $coupon->description, 'code' => $coupon->code, 'amount' => 12345]
                ]
            ],
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '',
                'street_2' => '',
                'city' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order' => ['id']]);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $user->id,
            'confirmed' => true,
            'total' => 1900,
            'coupon_subtotal' => 100
        ]);

        $order = $user->orders()->latest()->first();

        $this->assertDatabaseHas('coupon_order', [
            'coupon_id' => $coupon->id,
            'order_id' => $order->id,
            'savings' => 100
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_updates_profile_when_using_saving_for_later(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'first_name' => 'First test',
            'last_name' => 'Last test',
            'email' => $user->email,
            'phone' => '************',
            'street' => '123 Test St',
            'street_2' => 'Apt Test',
            'city' => 'Test',
            'state' => 'TE',
            'zip' => '12345',
            'checkout_card_id' => $card->id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    #[Test]
    public function it_doesnt_update_profile_when_not_saving_for_later(): void
    {
        Event::fake([OrderWasConfirmed::class, RecurringOrderCreated::class]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);

        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);
        $location = Pickup::factory()->create(['status_id' => 1, 'schedule_id' => $date->schedule_id]);

        $db_cart = \App\Models\Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $db_cart->updateCartLocation($location);
        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();
        $db_cart->addProduct($product, 2);

        $cart = [
            'type' => 'database',
            'id' => $db_cart->id,
            'purchase_type' => 'one_time_purchase',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => false,
                'opt_in_to_sms' => false,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => false
            ],
        ];

        $this->actingAs($user, 'api')
            ->postJson(route('api.carts.confirm'), $cart)
            ->assertCreated();

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'street' => $user->street,
            'street_2' => $user->street_2,
            'city' => $user->city,
            'state' => $user->state,
            'zip' => $user->zip,
            'checkout_card_id' => $user->checkout_card_id
        ]);

        Event::assertDispatched(OrderWasConfirmed::class);
        Event::assertNotDispatched(RecurringOrderCreated::class);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'database']);
    }
}
